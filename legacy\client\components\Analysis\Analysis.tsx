import { useTracker } from "meteor/react-meteor-data";
import React from "react";
import parseHtml from "html-react-parser";
import dbCollections from "../../../imports/db/dbCollections";
import { AIPrompt } from "../../../imports/db/prompt";
import Button from "../Forms/Button";
import { Messaging } from "../Messaging/messaging";
import makeFilterQuery from "../Filters/filterQuery.js";
import Selector from "../Forms/Selector";

export default function Analysis() {
    const [selectedPromptId, setSelectedPromptId] = React.useState("");
    const [analyzing, setAnalyzing] = React.useState(false);
    const [customPromptText, setCustomPromptText] = React.useState("");

    useTracker(() => {
        Meteor.subscribe("promptList");
        Meteor.subscribe("summaries", Meteor.userId());
    })

    useTracker(() => {
        makeFilterQuery();
    })

    const availablePrompts = useTracker(() => { return dbCollections.AiPrompts.find().fetch() });

    const currentAnalysis = useTracker(() => {
        const analysisFilter = {
            userId: Meteor.userId(),
            tag: Session.get('TagFilterName') || { $in: [null, undefined] },
            teamId: Session.get('TeamFilterId') || { $in: [null, undefined] },
            postTypeId: Session.get('TypeFilterId') || { $in: [null, undefined] },
            timeRange: Session.get('SelectedTimeRange') || { $in: [null, undefined] },
        }
        return dbCollections.Summaries.findOne(analysisFilter, { sort: { createdAt: -1 }, limit: 1 });
    })

    function updateAnalysis() {
        const updatedAnalysis = {
            ...currentAnalysis,
            userPrompt: customPromptText,
            promptId: selectedPromptId,
        }
        Meteor.call('analyzePosts', updatedAnalysis, (err, res) => {
            setAnalyzing(false);
            if (typeof res === "string") {
                Messaging.warning(res);
            }
            if (err) {
                Messaging.error(err.message ?? "An unknown error occurred.");
            }
        })
    }

    function createNewAnalysis() {
        const newAnalysis = {
            tagFilter: Session.get('TagFilterName') || false,
            teamFilter: Session.get('TeamFilterId') || false,
            typeFilter: Session.get('TypeFilterId') || false,
            timeRangeFilter: Session.get('SelectedTimeRange') || false,
            userPrompt: customPromptText,
            promptId: selectedPromptId
        }

        Meteor.call('createSummary', newAnalysis, function (err, res) {
            setAnalyzing(false);
            if (typeof res === "string") {
                Messaging.warning(res);
            }
            if (err) {
                Messaging.error(err.message ?? "An unknown error occurred.");
            }
        });
    }

    function doAnalyze() {
        setAnalyzing(true);
        if (currentAnalysis) {
            updateAnalysis();
        } else {
            createNewAnalysis();
        }
    }

    function renderPrompt(prompt: AIPrompt) {
        let description = prompt.description;
        if (typeof description === "string") {
            description = parseHtml(description);
            if (description.props) {
                if (description.props.className) {
                    description = React.cloneElement(
                        description, 
                        {className: description.props.className += " truncate"}
                    );
                } else {
                    description = React.cloneElement(
                        description,
                        {className: "truncate"}
                    );
                }
            }
        }
        return (
            <div
                key={prompt._id}
                className={`flex flex-nowrap gap-2 items-center`}
            >
                {prompt.companyId === "-1" && <img className="h-6" src="/images/krezzo_logo.png" />}
                <span className="shrink-0">{prompt.name}</span>
                <span> {description && "-"} </span>
                <span className="text-sm truncate">{description}</span>
            </div>
        )
    }

    function renderAnalysisDescriptor() {
        const thisPrompt = dbCollections.AiPrompts.findOne({ _id: currentAnalysis.promptId });
        if (!thisPrompt) return null;
        let team = { name: '' };
        let posttype = { name: '' };
        if (currentAnalysis.teamId) { team = dbCollections.TeamCollection.findOne({ _id: currentAnalysis.teamId }) }
        if (currentAnalysis.postTypeId) { posttype = dbCollections.PostTypeCollection.findOne({ _id: currentAnalysis.postTypeId }) }



        const filters = [
            ["Tag", currentAnalysis.tag || "All Tags"],
            ["Team", team.name || "Any Team"],
            ["Post Type", posttype.name || "Any Type"],
            ["Time Range", currentAnalysis.timeRange || "All Time"],
        ]

        return (
            <div className="text-center">
                <div>
                    Analysis of posts using the <span className="font-bold text-slate-600">{thisPrompt.name}</span> prompt and these filters:
                </div>
                <div className="flex gap-4 justify-center mt-2">
                    {filters.map(([label, value]) => (
                        <span key={label} className="text-xs text-slate-600">{label}: <b>{value}</b> </span>
                    ))}
                </div>
            </div>
        )
    }

    function renderAnalysis() {
        if (!currentAnalysis?.summary) return null;
        if (analyzing) return null;
        return (
            <div className="border border-solid border-slate-400 rounded mt-8">
                <div className="py-2 px-8 border-b border-slate-400">{renderAnalysisDescriptor()}</div>
                <div className="py-4 px-8">{parseHtml(currentAnalysis?.summary ?? "")}</div>
            </div>
        );
    }

    function renderAnalyzeButton() {
        if (analyzing) return <img className="mx-auto" src="/images/ai_wait.gif" />
        let disabled = selectedPromptId === "" || (selectedPromptId === "custom" && !customPromptText);
        return <Button variant="primary" disabled={disabled} onClick={doAnalyze}>Analyze</Button>
    }

    const customPrompt: AIPrompt = {
        _id: "custom",
        companyId: "",
        name: "Custom Prompt",
        description: "Enter your own prompt to analyze"
    }

    const allPrompts = [
        ...availablePrompts,
        customPrompt
    ]

    return (
        <div className="mt-8 mx-4 flex flex-col min-h-0 h-full overflow-auto pb-8">
            <div className="flex w-full justify-between items-center gap-8">
                <Selector 
                    items={allPrompts} 
                    selectedItem={allPrompts.find(p => p._id === selectedPromptId)} 
                    onSelect={(item) => setSelectedPromptId(item._id)} 
                    display={renderPrompt} 
                    placeholder="Select a prompt"
                />
                <div className="text-center w-32">
                    {renderAnalyzeButton()}
                </div>
            </div>
            {selectedPromptId === "custom" && <textarea className="w-full h-24 mt-2 rounded-lg" value={customPromptText} onChange={(e) => setCustomPromptText(e.target.value)} placeholder="Ask a question about the selected posts."></textarea>}

            <div className="shrink">
                {renderAnalysis()}
            </div>
        </div>
    );
}