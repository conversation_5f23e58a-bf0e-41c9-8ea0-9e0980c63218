# flyctl launch added from .gitignore
# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
**\node_modules

# Expo
**\.expo
**\dist
**\web-build
**\expo-env.d.ts

# Native
**\.kotlin
**\*.orig.*
**\*.jks
**\*.p8
**\*.p12
**\*.key
**\*.mobileprovision

# Metro
**\.metro-health-check*

# debug
**\npm-debug.*
**\yarn-debug.*
**\yarn-error.*

# macOS
**\.DS_Store
**\*.pem

# local env files
**\.env*.local

# typescript
**\*.tsbuildinfo

**\app-example
**\.yarn
**\.vscode

# flyctl launch added from .yarn\cache\.gitignore
.yarn\cache\.gitignore
.yarn\cache\**\*.flock
.yarn\cache\**\*.tmp

# flyctl launch added from .yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\.gitignore
## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.
##
## Get latest from https://github.com/github/gitignore/blob/master/VisualStudio.gitignore

# User-specific files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.rsuser
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.suo
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.user
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.userosscache
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.userprefs

# Mono auto generated files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\mono_crash.*

# Build results
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Dd]ebug
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Dd]ebugPublic
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Rr]elease
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Rr]eleases
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\x64
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\x86
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Aa][Rr][Mm]
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Aa][Rr][Mm]64
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\bld
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Bb]in
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Oo]bj
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Ll]og
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Ll]ogs

# Visual Studio 2015/2017 cache/options directory
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.vs
# Uncomment if you have tasks that create the project's static files in wwwroot
#wwwroot/

# Visual Studio 2017 auto generated files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\Generated\ Files

# MSTest test Results
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Tt]est[Rr]esult*
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Bb]uild[Ll]og.*

# NUnit
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.VisualState.xml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\TestResult.xml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\nunit-*.xml

# Build Results of an ATL Project
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Dd]ebugPS
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Rr]eleasePS
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\dlldata.c

# Benchmark Results
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\BenchmarkDotNet.Artifacts

# .NET Core
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\project.lock.json
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\project.fragment.lock.json
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\artifacts

# StyleCop
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\StyleCopReport.xml

# Files built by Visual Studio
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*_i.c
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*_p.c
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*_h.h
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.ilk
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.meta
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.obj
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.iobj
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pch
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pdb
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.ipdb
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pgc
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pgd
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.rsp
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.sbr
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.tlb
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.tli
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.tlh
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.tmp
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.tmp_proj
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*_wpftmp.csproj
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.log
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.vspscc
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.vssscc
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.builds
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pidb
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.svclog
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.scc

# Chutzpah Test files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\_Chutzpah*

# Visual C++ cache files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\ipch
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.aps
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.ncb
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.opendb
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.opensdf
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.sdf
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.cachefile
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.VC.db
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.VC.VC.opendb

# Visual Studio profiler
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.psess
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.vsp
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.vspx
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.sap

# Visual Studio Trace Files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.e2e

# TFS 2012 Local Workspace
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\$tf

# Guidance Automation Toolkit
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.gpState

# ReSharper is a .NET coding add-in
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\_ReSharper*
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.[Rr]e[Ss]harper
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.DotSettings.user

# TeamCity is a build add-in
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\_TeamCity*

# DotCover is a Code Coverage Tool
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.dotCover

# AxoCover is a Code Coverage Tool
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.axoCover\*
!.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.axoCover\settings.json

# Coverlet is a free, cross platform Code Coverage Tool
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\coverage*[.json, .xml, .info]

# Visual Studio code coverage results
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.coverage
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.coveragexml

# NCrunch
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\_NCrunch_*
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.*crunch*.local.xml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\nCrunchTemp_*

# MightyMoose
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.mm.*
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\AutoTest.Net

# Web workbench (sass)
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.sass-cache

# Installshield output folder
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\[Ee]xpress

# DocProject is a documentation generator add-in
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\buildhelp
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\Help\*.HxT
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\Help\*.HxC
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\Help\*.hhc
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\Help\*.hhk
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\Help\*.hhp
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\Help\Html2
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\DocProject\Help\html

# Click-Once directory
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\publish

# Publish Web Output
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.[Pp]ublish.xml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings,
# but database connection strings (with potential passwords) will be unencrypted
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pubxml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.publishproj

# Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\PublishScripts

# NuGet Packages
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.nupkg
# NuGet Symbol Packages
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.snupkg
# The packages folder can be ignored because of Package Restore
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\**\[Pp]ackages\*
# except build/, which is used as an MSBuild target.
!.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\**\[Pp]ackages\build
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.nuget.props
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.nuget.targets

# Microsoft Azure Build Output
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\csx
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.build.csdef

# Microsoft Azure Emulator
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\ecf
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\rcf

# Windows Store app package directories and files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\AppPackages
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\BundleArtifacts
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\Package.StoreAssociation.xml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\_pkginfo.txt
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.appx
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.appxbundle
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.appxupload

# Visual Studio cache files
# files ending in .cache can be ignored
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.[Cc]ache
# but keep track of directories ending in .cache
!.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\?*.[Cc]ache

# Others
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\ClientBin
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\~$*
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*~
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.dbmdl
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.dbproj.schemaview
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.jfm
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pfx
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.publishsettings
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\orleans.codegen.cs

# Including strong name files can present a security risk
# (https://github.com/github/gitignore/pull/2483#issue-259490424)
#*.snk

# Since there are multiple workflows, uncomment next line to ignore bower_components
# (https://github.com/github/gitignore/pull/1529#issuecomment-104372622)
#bower_components/

# RIA/Silverlight projects
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\Generated_Code

# Backup & report files from converting an old project file
# to a newer Visual Studio version. Backup files are not needed,
# because we have git ;-)
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\_UpgradeReport_Files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\Backup*
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\UpgradeLog*.XML
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\UpgradeLog*.htm
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\ServiceFabricBackup
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.rptproj.bak

# SQL Server files
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.mdf
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.ldf
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.ndf

# Business Intelligence projects
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.rdl.data
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.bim.layout
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.bim_*.settings
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.rptproj.rsuser
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*- [Bb]ackup.rdl
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*- [Bb]ackup ([0-9]).rdl
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*- [Bb]ackup ([0-9][0-9]).rdl

# Microsoft Fakes
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\FakesAssemblies

# GhostDoc plugin setting file
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.GhostDoc.xml

# Node.js Tools for Visual Studio
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.ntvs_analysis.dat
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\node_modules

# Visual Studio 6 build log
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.plg

# Visual Studio 6 workspace options file
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.opt

# Visual Studio 6 auto-generated workspace file (contains which files were open etc.)
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.vbw

# Visual Studio LightSwitch build output
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\**\*.HTMLClient\GeneratedArtifacts
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\**\*.DesktopClient\GeneratedArtifacts
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\**\*.DesktopClient\ModelManifest.xml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\**\*.Server\GeneratedArtifacts
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\**\*.Server\ModelManifest.xml
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\_Pvt_Extensions

# Paket dependency manager
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.paket\paket.exe
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\paket-files

# FAKE - F# Make
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.fake

# CodeRush personal settings
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.cr\personal

# Python Tools for Visual Studio (PTVS)
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\__pycache__
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.pyc

# Cake - Uncomment if you are using it
# tools/**
# !tools/packages.config

# Tabs Studio
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.tss

# Telerik's JustMock configuration file
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.jmconfig

# BizTalk build output
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.btp.cs
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.btm.cs
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.odx.cs
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.xsd.cs

# OpenCover UI analysis results
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\OpenCover

# Azure Stream Analytics local run output
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\ASALocalRun

# MSBuild Binary and Structured Log
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.binlog

# NVidia Nsight GPU debugger configuration file
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\*.nvuser

# MFractors (Xamarin productivity tool) working folder
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.mfractor

# Local History for Visual Studio
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.localhistory

# BeatPulse healthcheck temp database
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\healthchecksdb

# Backup folder for Package Reference Convert tool in Visual Studio 2017
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\MigrationBackup

# Ionide (cross platform F# VS Code tools) working folder
.yarn\unplugged\react-native-webview-virtual-f8b92e79b8\node_modules\react-native-webview\windows\**\.ionide

# flyctl launch added from android\.gitignore
# OSX
#
android\**\.DS_Store

# Android/IntelliJ
#
android\**\build
android\**\.idea
android\**\.gradle
android\**\local.properties
android\**\*.iml
android\**\*.hprof
android\**\.cxx

# Bundle artifacts
android\**\*.jsbundle
fly.toml
