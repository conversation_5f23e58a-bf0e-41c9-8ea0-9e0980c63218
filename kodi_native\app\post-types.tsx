import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import api, { endpoints } from '@/src/api/api';

export default function PostTypesScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [postTypes, setPostTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchPostTypes();
  }, []);

  const fetchPostTypes = async () => {
    if (!user?.companyId) {
      setError('No company associated with your account');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data } = await api.get(endpoints.postTypes.byCompany(user.companyId));
      setPostTypes(data);
    } catch (err: any) {
      console.error('Failed to fetch post types:', err);
      setError(err.response?.data?.message || 'Failed to load post types');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Post Types</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {error ? (
          <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
            <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          </Card>
        ) : null}

        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>
              Loading post types...
            </Text>
          </Card>
        ) : (
          <Card style={styles.contentCard}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
              Company Post Types
            </Text>
            <Text style={[styles.description, { color: colors.mutedForeground }]}>
              Post types help categorize and organize different kinds of posts in your company.
            </Text>
            
            {postTypes.length === 0 ? (
              <Text style={[styles.emptyText, { color: colors.mutedForeground }]}>
                No post types found. Contact your administrator to set up post types.
              </Text>
            ) : (
              <View style={styles.postTypesContainer}>
                {postTypes.map((postType, index) => (
                  <View key={index} style={[styles.postTypeItem, { backgroundColor: colors.secondary }]}>
                    <View style={styles.postTypeHeader}>
                      <View style={[styles.colorIndicator, { backgroundColor: postType.highlightColor || colors.primary }]} />
                      <Text style={[styles.postTypeName, { color: colors.foreground }]}>
                        {postType.name}
                      </Text>
                    </View>
                    {postType.description && (
                      <Text style={[styles.postTypeDescription, { color: colors.mutedForeground }]}>
                        {postType.description}
                      </Text>
                    )}
                    {postType.recordTips && (
                      <View style={styles.tipsContainer}>
                        <Text style={[styles.tipsLabel, { color: colors.primary }]}>
                          Recording Tips:
                        </Text>
                        <Text style={[styles.tipsText, { color: colors.mutedForeground }]}>
                          {postType.recordTips}
                        </Text>
                      </View>
                    )}
                    {postType.archived && (
                      <View style={[styles.archivedBadge, { backgroundColor: colors.destructive + '20' }]}>
                        <Text style={[styles.archivedText, { color: colors.destructive }]}>
                          Archived
                        </Text>
                      </View>
                    )}
                  </View>
                ))}
              </View>
            )}
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scrollContent: {
    padding: 20,
    gap: 16,
  },
  errorCard: {
    padding: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  loadingCard: {
    padding: 16,
    borderRadius: 8,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  contentCard: {
    padding: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
  },
  postTypesContainer: {
    gap: 12,
  },
  postTypeItem: {
    padding: 16,
    borderRadius: 8,
    position: 'relative',
  },
  postTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  postTypeName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  postTypeDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  tipsContainer: {
    marginTop: 8,
  },
  tipsLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  tipsText: {
    fontSize: 12,
    lineHeight: 16,
  },
  archivedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  archivedText: {
    fontSize: 10,
    fontWeight: '600',
  },
});
