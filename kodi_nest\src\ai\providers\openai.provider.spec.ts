import { OpenAiProvider } from './openai.provider';
import OpenAI from 'openai';
import { ChatMessage } from '../schemas/ai-chat.schema';

// Mock the OpenAI SDK
jest.mock('openai', () => {
  // Define mock implementations for the methods we use
  const mockChatCompletionsCreate = jest.fn();
  const mockAudioTranscriptionsCreate = jest.fn(); // Though not fully used, good to have a mock

  return jest.fn().mockImplementation(() => {
    return {
      chat: {
        completions: {
          create: mockChatCompletionsCreate,
        },
      },
      audio: {
        transcriptions: {
          create: mockAudioTranscriptionsCreate,
        },
      },
      // Expose mocks for easy access in tests
      __mocks__: {
        mockChatCompletionsCreate,
        mockAudioTranscriptionsCreate,
      },
    };
  });
});

describe('OpenAiProvider', () => {
  const apiKey = 'test-api-key';
  const defaultModel = 'gpt-3.5-turbo';
  let provider: OpenAiProvider;
  let mockOpenAIInstance: ReturnType<typeof OpenAI> & { __mocks__: any };

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    provider = new OpenAiProvider(apiKey, defaultModel);
    // Access the mocked instance and its method mocks
    // This relies on the structure of our mock.
    // The OpenAI constructor itself is mocked, so new OpenAI() returns our mock.
    // We need to cast to get the mock methods.
    mockOpenAIInstance = new OpenAI({ apiKey: '', baseURL: '' }) as any; 
  });

  describe('constructor', () => {
    it('should initialize OpenAI client with API key and default model', () => {
      expect(OpenAI).toHaveBeenCalledWith({ apiKey: apiKey, baseURL: undefined });
      // We can't directly test provider.defaultModel as it's private,
      // but its usage in methods will confirm it's set.
    });

    it('should initialize OpenAI client with baseURL if provided', () => {
      const apiBaseUrl = 'http://localhost:1234/v1';
      new OpenAiProvider(apiKey, defaultModel, apiBaseUrl);
      expect(OpenAI).toHaveBeenCalledWith({ apiKey: apiKey, baseURL: apiBaseUrl });
    });
  });

  describe('generateText', () => {
    it('should call openai.chat.completions.create with correct parameters and return content', async () => {
      const prompt = 'Test prompt';
      const mockResponse = { choices: [{ message: { content: 'Generated text' } }] };
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockResolvedValue(mockResponse);

      const result = await provider.generateText(prompt);

      expect(mockOpenAIInstance.__mocks__.mockChatCompletionsCreate).toHaveBeenCalledWith({
        model: defaultModel,
        messages: [{ role: 'user', content: prompt }],
      });
      expect(result).toBe('Generated text');
    });

    it('should use provided model if specified in options', async () => {
      const prompt = 'Test prompt';
      const customModel = 'gpt-4';
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockResolvedValue({ choices: [{ message: { content: '...' } }] });
      
      await provider.generateText(prompt, { model: customModel });
      
      expect(mockOpenAIInstance.__mocks__.mockChatCompletionsCreate).toHaveBeenCalledWith(
        expect.objectContaining({ model: customModel }),
      );
    });

    it('should return empty string if no content in response', async () => {
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockResolvedValue({ choices: [{ message: {} }] });
      const result = await provider.generateText('Test');
      expect(result).toBe('');
    });
    
    it('should return empty string if no choices in response', async () => {
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockResolvedValue({ choices: [] });
      const result = await provider.generateText('Test');
      expect(result).toBe('');
    });

    it('should throw error if API call fails', async () => {
      const error = new Error('API Error');
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockRejectedValue(error);
      await expect(provider.generateText('Test')).rejects.toThrow(error);
    });
  });

  describe('createChatCompletion', () => {
    const messages: ChatMessage[] = [
      { role: 'user', content: 'Hello', timestamp: new Date() },
    ];

    it('should call openai.chat.completions.create with transformed messages and return ChatMessage', async () => {
      const mockResponseContent = 'AI response';
      const mockResponse = { choices: [{ message: { content: mockResponseContent } }] };
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockResolvedValue(mockResponse);

      const result = await provider.createChatCompletion(messages);

      expect(mockOpenAIInstance.__mocks__.mockChatCompletionsCreate).toHaveBeenCalledWith({
        model: defaultModel,
        messages: messages.map(m => ({ role: m.role as 'user' | 'assistant' | 'system', content: m.content })),
        temperature: undefined, // Default temperature
      });
      expect(result).toEqual(expect.objectContaining({
        role: 'assistant',
        content: mockResponseContent,
      }));
      expect(result?.timestamp).toBeInstanceOf(Date);
    });

    it('should use provided model and temperature if specified', async () => {
        const customModel = 'gpt-4-turbo';
        const customTemp = 0.5;
        mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockResolvedValue({ choices: [{ message: { content: '...' } }] });

        await provider.createChatCompletion(messages, { model: customModel, temperature: customTemp });

        expect(mockOpenAIInstance.__mocks__.mockChatCompletionsCreate).toHaveBeenCalledWith(
            expect.objectContaining({ model: customModel, temperature: customTemp })
        );
    });
    
    it('should return null if no content in response', async () => {
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockResolvedValue({ choices: [{ message: {} }] });
      const result = await provider.createChatCompletion(messages);
      expect(result).toBeNull();
    });

    it('should throw error if API call fails', async () => {
      const error = new Error('API Error');
      mockOpenAIInstance.__mocks__.mockChatCompletionsCreate.mockRejectedValue(error);
      await expect(provider.createChatCompletion(messages)).rejects.toThrow(error);
    });
  });

  describe('transcribe', () => {
    let consoleWarnSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    });

    afterEach(() => {
      consoleWarnSpy.mockRestore();
    });

    it('should log a warning and return a placeholder string', async () => {
      const audioUrl = 'http://example.com/audio.mp3';
      const result = await provider.transcribe(audioUrl);
      
      expect(consoleWarnSpy).toHaveBeenCalledWith('OpenAIProvider.transcribe: Needs implementation to fetch audio from URL and stream to OpenAI API.');
      expect(result).toContain('Transcription for http://example.com/audio.mp3');
      expect(result).toContain('actual implementation pending');
      // It should not call the actual openai.audio.transcriptions.create in its current state
      expect(mockOpenAIInstance.__mocks__.mockAudioTranscriptionsCreate).not.toHaveBeenCalled();
    });

     it('should include model and language in placeholder if provided', async () => {
      const audioUrl = 'http://example.com/audio.mp3';
      const model = 'whisper-custom';
      const language = 'es';
      const result = await provider.transcribe(audioUrl, { model, language });
      
      expect(result).toContain(`model: ${model}`);
      expect(result).toContain(`lang: ${language}`);
    });

    // In a future test, if transcribe is fully implemented:
    // it('should call openai.audio.transcriptions.create and return text if implemented', async () => { ... });
    // it('should throw error if transcription API call fails if implemented', async () => { ... });
  });
});
