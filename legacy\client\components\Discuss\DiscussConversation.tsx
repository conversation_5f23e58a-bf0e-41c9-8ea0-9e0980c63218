import { useTracker } from "meteor/react-meteor-data";
import React from "react";
import dbCollections from "../../../imports/db/dbCollections";
import Button from "../Forms/Button";
import { Messaging } from "../Messaging/messaging";

export interface Message {
    body: string;
    isOwner: boolean;
}

interface Props {
    awaitingAi: boolean;
    messages: Message[];
    pendingMessage?: string;
 }

export default function DiscussConversation(props: Props) {
    const messagesEndRef = React.useRef(null);

    function scrollToBottom() {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }

    React.useEffect(() => {
        scrollToBottom();
    }, [props.messages, props.pendingMessage]);

    function renderMessage(message: Message, idx: number) {
        function copyMessage() {
            navigator.clipboard.writeText(message.body);
            Messaging.info("Message copied to clipboard");
        }

        let messageClass = "relative mt-8 p-4 rounded border";
        let authorTag: React.ReactElement | null = null;
        if (message.isOwner) {
            messageClass += " ml-32 bg-white border-slate-300";
            authorTag = (
                <div className="flex items-center justify-center text-lg text-slate-600 absolute top-0 right-16 z-10 bg-white -translate-y-1/2 px-2 border border-slate-300 rounded-full">
                    You
                </div>
            )
        } else {
            messageClass += " mr-32 bg-indigo-50  border-indigo-300";
            authorTag = (
                <div className="flex items-center justify-center text-lg text-indigo-600 absolute top-0 left-16 z-10 bg-white -translate-y-1/2 px-2 border border-indigo-300 rounded-full">
                    <img className="h-4" src="images/krezzo_logo.png" />
                    <span className="-mt-1">KrezzoChat</span>
                </div>
            )
        }
        return (
            <div key={idx} className={messageClass}>
                {authorTag}
                {message.body}
                <div className="flex justify-end items-center gap-2">
                    <a href="#" className="text-gray-600 hover:text-indigo-600">
                        <i className="fas fa-copy" onClick={copyMessage}></i>
                    </a>
                </div>
            </div>
        )
    }

    function archiveChat() {
        const query = {
            userId: Meteor.userId(),
            tag: Session.get('TagFilterName') || { $in: [null, undefined] },
            teamId: Session.get('TeamFilterId') || { $in: [null, undefined] },
            postTypeId: Session.get('TypeFilterId') || { $in: [null, undefined] },
            timeRange: Session.get('SelectedTimeRange') || { $in: [null, undefined] },
            archived: false
        };
        const chat = dbCollections.AIChats.findOne(query);
        if (chat) {
            dbCollections.AIChats.update(chat._id, { $set: { archived: true } });
        }
    }

    function renderIndicator() {
        if (props.awaitingAi) {
            return (
                <img src="/images/ai_wait.gif" />
            )
        } else {
            return (
                <div className="mt-2 cursor-pointer inline-block text-slate-600 hover:text-slate-900" onClick={archiveChat}>
                    <i className="fas fa-arrows-rotate"></i> 
                    {" "} Refresh Conversation
                </div>
            )
        }
    }

    const pendingMessageAlreadyDisplayed = props.messages 
        && props.messages[0]?.body === props.pendingMessage;
    const showPendingMessage = props.pendingMessage && !pendingMessageAlreadyDisplayed;

    const greetingMessage = {
        isOwner: false,
        body: "Hi!  I'm here to help you find insights in the conversations happening in Krezzo.  Tell me what you would like to know."
    }

    return (
        <div className="overflow-y-auto grow">
            {renderMessage(greetingMessage, -1)}
            {props.messages.map(renderMessage)}
            {showPendingMessage && renderMessage({ body: props.pendingMessage, isOwner: true }, -2)}
            {renderIndicator()}
            <div ref={messagesEndRef} />
        </div>
    )
}