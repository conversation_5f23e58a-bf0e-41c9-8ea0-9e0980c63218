import { useTracker } from "meteor/react-meteor-data";
import React from "react";
import dbCollections from "../../../imports/db/dbCollections";

interface ActiveFilter {
    key: string;
    value: string;
}

interface Props {}

export default function DiscussFilterDisplay(props: Props) {
    function renderFilter(filter: ActiveFilter) {
        const { key, value } = filter;
        return (
            <div key={key} className="flex items-center justify-center bg-gray-200 text-gray-700 rounded-full px-4 py-1 m-1">
                <span>{value}</span>
            </div>
        )
    }
    
    const activeFilters = useTracker(() => {
        const tagFilter = Session.get("TagFilterName");
        const teamFilter = Session.get("TeamFilterId");
        const typeFilter = Session.get("TypeFilterId");
        const timeRangeFilter = Session.get("SelectedTimeRange");
        const activeFilters: ActiveFilter[] = [];

        if (tagFilter) {
            activeFilters.push({ key: "Tag", value: tagFilter });
        }
        if (teamFilter) {
            const teamName = dbCollections.TeamCollection.findOne(teamFilter)?.name;
            activeFilters.push({ key: "Team", value: teamName });
        }
        if (typeFilter) {
            const typeName = dbCollections.PostTypeCollection.findOne(typeFilter)?.name;
            activeFilters.push({ key: "Type", value: typeName });
        }
        if (timeRangeFilter) {
            activeFilters.push({ key: "Time Range", value: timeRangeFilter });
        }
        
        return activeFilters;
    })
  
    return (
        <div className="flex flex-wrap justify-center items-center m-auto">
            {activeFilters.length === 0 && <span className="text-gray-500">No filters applied</span>}
            {activeFilters.map(renderFilter)}
        </div>
    );
}