import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';

export default function CreatePromptScreen() {
  const { user, hasAdminRights } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [template, setTemplate] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleGoBack = () => {
    router.back();
  };

  const handleSave = async () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to create prompts');
      return;
    }

    if (!name.trim()) {
      Alert.alert('Error', 'Prompt name is required');
      return;
    }

    if (!template.trim()) {
      Alert.alert('Error', 'Prompt template is required');
      return;
    }

    try {
      setIsSaving(true);
      
      const newPrompt = {
        name: name.trim(),
        description: description.trim() || undefined,
        template: template.trim(),
        companyId: user?.companyId,
        userId: user?.id,
        isSystem: false,
      };
      
      await api.post(endpoints.prompts.base, newPrompt);
      
      Alert.alert('Success', 'Prompt created successfully', [
        { text: 'OK', onPress: () => router.back() }
      ]);
      
    } catch (err: any) {
      console.error('Failed to create prompt:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to create prompt');
    } finally {
      setIsSaving(false);
    }
  };

  const templateExamples = [
    {
      name: 'Daily Standup',
      template: 'What did you work on yesterday? What are you working on today? Any blockers or challenges?'
    },
    {
      name: 'Project Update',
      template: 'Project status: [Current phase]\nKey achievements: [List accomplishments]\nUpcoming milestones: [Next goals]\nRisks and issues: [Any concerns]'
    },
    {
      name: 'Feedback Request',
      template: 'What specific feedback are you looking for? What context should reviewers know? What areas need the most attention?'
    }
  ];

  const handleExampleSelect = (example: typeof templateExamples[0]) => {
    setName(example.name);
    setTemplate(example.template);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Create Prompt</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <View style={styles.iconContainer}>
            <View style={[styles.promptIcon, { backgroundColor: colors.primary + '20' }]}>
              <Ionicons name="bulb" size={48} color={colors.primary} />
            </View>
          </View>

          <Input
            label="Name"
            value={name}
            onChangeText={setName}
            placeholder="Enter prompt name"
            style={styles.input}
          />
          
          <Input
            label="Description"
            value={description}
            onChangeText={setDescription}
            placeholder="Enter description (optional)"
            multiline
            numberOfLines={3}
            style={styles.input}
          />
          
          <Input
            label="Template"
            value={template}
            onChangeText={setTemplate}
            placeholder="Enter prompt template"
            multiline
            numberOfLines={6}
            style={styles.input}
          />

          <View style={styles.examplesSection}>
            <Text style={[styles.examplesTitle, { color: colors.foreground }]}>
              Quick Start Examples
            </Text>
            <Text style={[styles.examplesSubtitle, { color: colors.mutedForeground }]}>
              Tap an example to use as a starting point
            </Text>
            
            {templateExamples.map((example, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.exampleItem, { backgroundColor: colors.secondary }]}
                onPress={() => handleExampleSelect(example)}
              >
                <View style={styles.exampleHeader}>
                  <Text style={[styles.exampleName, { color: colors.foreground }]}>
                    {example.name}
                  </Text>
                  <Ionicons name="chevron-forward" size={16} color={colors.mutedForeground} />
                </View>
                <Text style={[styles.exampleTemplate, { color: colors.mutedForeground }]} numberOfLines={2}>
                  {example.template}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.infoSection}>
            <View style={[styles.infoBox, { backgroundColor: colors.secondary }]}>
              <Ionicons name="information-circle" size={20} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.mutedForeground }]}>
                Prompts help guide AI analysis and provide structure for posts. Use placeholders like [topic] 
                or [context] to make templates more flexible.
              </Text>
            </View>
          </View>

          <View style={styles.actions}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleGoBack}
              style={styles.cancelButton}
            />
            <Button
              title="Create Prompt"
              onPress={handleSave}
              isLoading={isSaving}
              style={styles.saveButton}
            />
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    padding: 20,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  promptIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  input: {
    marginBottom: 16,
  },
  examplesSection: {
    marginBottom: 20,
  },
  examplesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  examplesSubtitle: {
    fontSize: 14,
    marginBottom: 12,
  },
  exampleItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  exampleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  exampleName: {
    fontSize: 14,
    fontWeight: '600',
  },
  exampleTemplate: {
    fontSize: 12,
    lineHeight: 16,
  },
  infoSection: {
    marginBottom: 24,
  },
  infoBox: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    alignItems: 'flex-start',
    gap: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
});
