import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';

export default function CreatePostTypeScreen() {
  const { user, hasAdminRights } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [recordTips, setRecordTips] = useState('');
  const [highlightColor, setHighlightColor] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleGoBack = () => {
    router.back();
  };

  const handleSave = async () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to create post types');
      return;
    }

    if (!name.trim()) {
      Alert.alert('Error', 'Post type name is required');
      return;
    }

    try {
      setIsSaving(true);
      
      const newPostType = {
        name: name.trim(),
        description: description.trim() || undefined,
        recordTips: recordTips.trim() || undefined,
        highlightColor: highlightColor.trim() || undefined,
        companyId: user?.companyId,
        userId: user?.id,
      };
      
      await api.post(endpoints.postTypes.base, newPostType);
      
      Alert.alert('Success', 'Post type created successfully', [
        { text: 'OK', onPress: () => router.back() }
      ]);
      
    } catch (err: any) {
      console.error('Failed to create post type:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to create post type');
    } finally {
      setIsSaving(false);
    }
  };

  const colorOptions = [
    '#FF5733', '#33FF57', '#3357FF', '#FF33F1', '#F1FF33',
    '#33FFF1', '#F133FF', '#FF8C33', '#8C33FF', '#33FF8C'
  ];

  const handleColorSelect = (color: string) => {
    setHighlightColor(color);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Create Post Type</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <Input
            label="Name"
            value={name}
            onChangeText={setName}
            placeholder="Enter post type name"
            style={styles.input}
          />
          
          <Input
            label="Description"
            value={description}
            onChangeText={setDescription}
            placeholder="Enter description (optional)"
            multiline
            numberOfLines={3}
            style={styles.input}
          />
          
          <Input
            label="Recording Tips"
            value={recordTips}
            onChangeText={setRecordTips}
            placeholder="Enter recording tips (optional)"
            multiline
            numberOfLines={3}
            style={styles.input}
          />
          
          <View style={styles.colorSection}>
            <Text style={[styles.colorLabel, { color: colors.foreground }]}>
              Highlight Color (Optional)
            </Text>
            
            <Input
              value={highlightColor}
              onChangeText={setHighlightColor}
              placeholder="Enter color (e.g., #FF5733)"
              style={styles.colorInput}
            />
            
            <View style={styles.colorGrid}>
              {colorOptions.map((color, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    highlightColor === color && styles.selectedColor
                  ]}
                  onPress={() => handleColorSelect(color)}
                />
              ))}
            </View>
            
            {highlightColor && (
              <View style={styles.colorPreview}>
                <Text style={[styles.previewLabel, { color: colors.foreground }]}>
                  Preview:
                </Text>
                <View style={[
                  styles.previewSwatch, 
                  { backgroundColor: highlightColor }
                ]} />
                <Text style={[styles.previewText, { color: colors.mutedForeground }]}>
                  {highlightColor}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.actions}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleGoBack}
              style={styles.cancelButton}
            />
            <Button
              title="Create Post Type"
              onPress={handleSave}
              isLoading={isSaving}
              style={styles.saveButton}
            />
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    padding: 20,
  },
  input: {
    marginBottom: 16,
  },
  colorSection: {
    marginBottom: 20,
  },
  colorLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  colorInput: {
    marginBottom: 16,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColor: {
    borderColor: '#000',
    borderWidth: 3,
  },
  colorPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  previewLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  previewSwatch: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  previewText: {
    fontSize: 14,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
});
