import axios from 'axios';
import api from '../api/api';
import { Platform } from 'react-native';


/**
 * Connection status object
 */
export interface ConnectionStatus {
  server: 'unknown' | 'connected' | 'error';
  message: string;
  timestamp: number;
}

/**
 * Check if the server is reachable
 * @returns Promise<ConnectionStatus>
 */
export async function checkServerConnection(): Promise<ConnectionStatus> {
  try {
    console.log('Testing connection to API server:', api.defaults.baseURL);

    // Extract the base URL without the /api suffix
    const baseUrl = api.defaults.baseURL?.endsWith('/api')
      ? api.defaults.baseURL.slice(0, -4)
      : api.defaults.baseURL;

    // Try both health endpoints (with and without /api prefix)
    let response;
    try {
      // First try the public health endpoint (no /api prefix)
      response = await axios.get(`${baseUrl}/health`, {
        timeout: 3000, // 3 second timeout
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        // Enable credentials for web (cookies, auth headers)
        withCredentials: Platform.OS === 'web'
      });
      console.log('Public health endpoint successful');
    } catch (healthErr: unknown) {
      console.log('Public health endpoint failed, trying API endpoint');

      // If that fails, try the API health endpoint
      response = await axios.get(`${baseUrl}/api/health`, {
        timeout: 3000, // 3 second timeout
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        // Enable credentials for web (cookies, auth headers)
        withCredentials: Platform.OS === 'web'
      });
      console.log('API health endpoint successful');
    }

    console.log('Server health check response:', response.data);

    if (response.status === 200) {
      return {
        server: 'connected',
        message: 'Connected to server successfully',
        timestamp: Date.now(),
      };
    } else {
      return {
        server: 'error',
        message: `Unexpected response: ${response.status}`,
        timestamp: Date.now(),
      };
    }
  } catch (err: any) {
    console.error('Server connection test failed:', err);

    let errorMessage = 'Failed to connect to server';
    
    if (err.code === 'ECONNABORTED') {
      errorMessage = 'Connection timed out';
    } else if (err.code === 'ECONNREFUSED') {
      errorMessage = 'Connection refused';
    } else if (err.response) {
      errorMessage = `Server error: ${err.response.status}`;
    } else if (err.request) {
      errorMessage = 'No response from server';
    }
    
    return {
      server: 'error',
      message: errorMessage,
      timestamp: Date.now(),
    };
  }
}

/**
 * Check connection and log the result
 * @returns Promise<ConnectionStatus>
 */
export async function logConnectionStatus(): Promise<ConnectionStatus> {
  const status = await checkServerConnection();
  
  if (status.server === 'connected') {
    console.log('✅ Server connection: OK');
  } else {
    console.error(`❌ Server connection: ${status.message}`);
  }
  
  return status;
}
