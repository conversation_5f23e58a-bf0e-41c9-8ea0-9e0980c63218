import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import { Team, Profile } from '@/src/types';

export default function TeamDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [team, setTeam] = useState<Team | null>(null);
  const [members, setMembers] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isOwner, setIsOwner] = useState(false);

  useEffect(() => {
    if (id) {
      fetchTeamDetails();
    }
  }, [id]);

  const fetchTeamDetails = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Fetch team details
      const teamResponse = await api.get(endpoints.teams.byId(id));
      const teamData = teamResponse.data;
      setTeam(teamData);
      
      // Check if current user is the owner
      setIsOwner(teamData.createdBy === user?.id);
      
      // Fetch team members
      const membersResponse = await api.get(endpoints.teams.members(id));
      setMembers(membersResponse.data);
      
    } catch (err: any) {
      console.error('Failed to fetch team details:', err);
      setError(err.response?.data?.message || 'Failed to load team details');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleEditTeam = () => {
    // TODO: Navigate to edit team screen when implemented
    Alert.alert('Coming Soon', 'Team editing will be available soon');
  };

  const handleJoinLeaveTeam = async () => {
    if (!team) return;
    
    try {
      const isMember = members.some(member => member.userId === user?.id);
      
      if (isMember) {
        // Leave team
        await api.delete(endpoints.teams.removeMember(team._id, user?.id || ''));
        Alert.alert('Success', 'You have left the team');
      } else {
        // Join team
        await api.post(endpoints.teams.addMember(team._id), { userId: user?.id });
        Alert.alert('Success', 'You have joined the team');
      }
      
      // Refresh team details
      fetchTeamDetails();
    } catch (err: any) {
      console.error('Failed to join/leave team:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to update team membership');
    }
  };

  const renderMemberItem = ({ item }: { item: Profile }) => (
    <View style={[styles.memberItem, { backgroundColor: colors.card }]}>
      <View style={[styles.memberAvatar, { backgroundColor: colors.primary + '20' }]}>
        <Text style={[styles.memberAvatarText, { color: colors.primary }]}>
          {item.firstName?.[0]?.toUpperCase() || item.email[0].toUpperCase()}
        </Text>
      </View>
      <View style={styles.memberInfo}>
        <Text style={[styles.memberName, { color: colors.foreground }]}>
          {item.firstName && item.lastName 
            ? `${item.firstName} ${item.lastName}` 
            : item.email
          }
        </Text>
        {item.title && (
          <Text style={[styles.memberTitle, { color: colors.mutedForeground }]}>
            {item.title}
          </Text>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Team Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  if (error || !team) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Team Details</Text>
          <View style={styles.placeholder} />
        </View>
        <Card style={styles.errorCard}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>
            {error || 'Team not found'}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchTeamDetails}
            style={styles.retryButton}
          />
        </Card>
      </SafeAreaView>
    );
  }

  const isMember = members.some(member => member.userId === user?.id);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Team Details</Text>
        {isOwner && (
          <TouchableOpacity
            style={[styles.editButton, { backgroundColor: colors.primary }]}
            onPress={handleEditTeam}
          >
            <Ionicons name="create-outline" size={20} color={colors.primaryForeground} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Team Info Card */}
        <Card style={styles.teamCard}>
          <View style={[styles.teamIcon, { backgroundColor: colors.primary + '20' }]}>
            <Text style={[styles.teamIconText, { color: colors.primary }]}>
              {team.name[0].toUpperCase()}
            </Text>
          </View>
          <Text style={[styles.teamName, { color: colors.foreground }]}>
            {team.name}
          </Text>
          {team.description && (
            <Text style={[styles.teamDescription, { color: colors.mutedForeground }]}>
              {team.description}
            </Text>
          )}
          
          {/* Membership Button */}
          <Button
            title={isMember ? 'Leave Team' : 'Join Team'}
            onPress={handleJoinLeaveTeam}
            variant={isMember ? 'outline' : 'default'}
            style={styles.membershipButton}
          />
        </Card>

        {/* Members Section */}
        <Card style={styles.membersCard}>
          <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
            Members ({members.length})
          </Text>
          {members.length > 0 ? (
            <FlatList
              data={members}
              renderItem={renderMemberItem}
              keyExtractor={(item) => item._id}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <Text style={[styles.emptyText, { color: colors.mutedForeground }]}>
              No members yet
            </Text>
          )}
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorCard: {
    margin: 16,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  teamCard: {
    alignItems: 'center',
    marginBottom: 16,
    padding: 24,
  },
  teamIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  teamIconText: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  teamName: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  teamDescription: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  membershipButton: {
    marginTop: 8,
  },
  membersCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  memberAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '500',
  },
  memberTitle: {
    fontSize: 14,
    marginTop: 2,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    fontStyle: 'italic',
  },
});
