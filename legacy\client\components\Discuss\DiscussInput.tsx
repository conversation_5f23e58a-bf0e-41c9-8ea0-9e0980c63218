import React from "react";
import Button from "../Forms/Button";

interface Props {
    ready: boolean;
    onSend: (message: string) => void;
}

export default function DiscussInput(props: Props) {
    const [message, setMessage] = React.useState("");
    
    function onMessageChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
        setMessage(e.target.value);
    }

    function handleSend() {
        props.onSend(message);
        setMessage("");
    }

    function handleKeyDown(e: React.KeyboardEvent<HTMLTextAreaElement>) {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    }

    return (
        <div id="chat_compose" className="mt-4">
            <div id="compose_area">
                <form className="js-todo-new input-symbol">
                    <textarea 
                        className="replybox w-full rounded clickable" 
                        value={message} 
                        onChange={onMessageChange}
                        onKeyDown={handleKeyDown}
                    >

                    </textarea>
                </form>
            </div>
            <div className="text-center">
                <Button
                    variant="primary"
                    onClick={handleSend}
                    disabled={!props.ready || message.length === 0}
                >
                    Send
                </Button>
            </div>
        </div>
    )
}