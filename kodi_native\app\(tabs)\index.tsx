import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
  Alert,
  useWindowDimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Card from '@/src/components/ui/Card';
import { Post, Team, PostType, Tag } from '@/src/types';
import PostItem from '@/src/components/posts/PostItem';
import SamplePost from '@/src/components/posts/SamplePost';
import FeedFilters from '@/src/components/feed/FeedFilters';
import ConnectionStatus from '@/src/components/ui/ConnectionStatus';
import { ConnectionStatus as ConnectionStatusType } from '@/src/utils/connectionCheck';

export default function FeedScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  const { width } = useWindowDimensions();

  const [posts, setPosts] = useState<Post[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [postTypes, setPostTypes] = useState<PostType[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [company, setCompany] = useState<{name: string} | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatusType>({
    server: 'unknown',
    message: 'Checking connection...',
    timestamp: Date.now(),
  });

  // Filter states
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [selectedPostType, setSelectedPostType] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);

  // Determine if we should use desktop layout based on screen width
  const isDesktopLayout = width >= 768;

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedTeam || selectedPostType || selectedTag) {
      fetchPosts();
    }
  }, [selectedTeam, selectedPostType, selectedTag]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Check connection status before making API calls
      if (connectionStatus.server === 'error') {
        setError(`Cannot load feed: ${connectionStatus.message}. Please check your connection and try again.`);
        setLoading(false);
        setRefreshing(false);
        return;
      }

      // Make API calls one by one to isolate any issues
      try {
        const postsRes = await api.get(endpoints.posts.feed, {
          params: {
            companyId: user?.profile?.companyId,
          },
        });
        setPosts(postsRes.data);
      } catch (err) {
        console.error('Failed to fetch posts:', err);
        // Continue with other requests even if this one fails
      }

      try {
        const teamsRes = await api.get(endpoints.teams.base);
        setTeams(teamsRes.data);
      } catch (err) {
        console.error('Failed to fetch teams:', err);
        // Continue with other requests even if this one fails
      }

      try {
        // Use the Axios instance which already has the auth token
        const postTypesRes = await api.get(endpoints.postTypes.base);
        setPostTypes(postTypesRes.data);
      } catch (err) {
        console.error('Failed to fetch post types:', err);
        // Continue with other requests even if this one fails
      }

      try {
        // Use the Axios instance which already has the auth token
        const tagsRes = await api.get(endpoints.tags.base);
        setTags(tagsRes.data);
      } catch (err) {
        console.error('Failed to fetch tags:', err);
        // Continue with other requests even if this one fails
      }

      try {
        if (user?.profile?.companyId) {
          const companyRes = await api.get(endpoints.companies.byId(user.companyId));
          setCompany(companyRes.data);
        }
      } catch (err) {
        console.error('Failed to fetch company:', err);
        // Continue with other requests even if this one fails
      }

      setError(''); // Clear any previous errors
    } catch (err: any) {
      console.error('Failed to fetch data:', err);
      setError('Failed to load feed data. Please try again later.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchPosts = async () => {
    try {
      setLoading(true);

      // Check connection status before making API calls
      if (connectionStatus.server === 'error') {
        setError(`Cannot load posts: ${connectionStatus.message}. Please check your connection and try again.`);
        setLoading(false);
        return;
      }

      let url = endpoints.posts.feed;
      const params = new URLSearchParams();

      if (selectedTeam) params.append('teamId', selectedTeam);
      if (selectedPostType) params.append('postTypeId', selectedPostType);
      if (selectedTag) params.append('tag', selectedTag);

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const { data } = await api.get(url);
      setPosts(data);
      setError(''); // Clear any previous errors
    } catch (err: any) {
      console.error('Failed to fetch posts:', err);
      setError('Failed to load posts. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const clearFilters = () => {
    setSelectedTeam('');
    setSelectedPostType('');
    setSelectedTag('');
    fetchData();
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  const hasActiveFilters = selectedTeam || selectedPostType || selectedTag;



  const renderEmptyState = () => {
    if (hasActiveFilters) {
      return (
        <View style={styles.emptyStateContainer}>
          <Card style={styles.emptyStateCard}>
            <Text style={[styles.emptyStateText, { color: colors.mutedForeground }]}>
              No posts match your current filters. Try adjusting your filters or clear them to see all posts.
            </Text>
          </Card>
        </View>
      );
    }

    return (
      <View style={styles.emptyStateContainer}>
        <SamplePost companyName={company?.name || 'your company'} />
        <Card style={styles.emptyStateCard}>
          <Text style={[styles.emptyStateText, { color: colors.mutedForeground }]}>
            No posts yet. Be the first to post!
          </Text>
        </Card>
      </View>
    );
  };



  // Mobile layout
  const renderMobileLayout = () => (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.filterButton, { borderColor: colors.border, backgroundColor: colors.card }]}
          onPress={() => setShowFilters(!showFilters)}
          accessibilityRole="button"
          accessibilityLabel="Toggle filters"
          accessibilityHint="Shows or hides post filtering options"
        >
          <Ionicons name="filter-outline" size={18} color={colors.foreground} style={styles.filterIcon} />
          <Text style={[styles.filterButtonText, { color: colors.foreground }]}>
            Filters {hasActiveFilters ? '(Active)' : ''}
          </Text>
        </TouchableOpacity>
      </View>

      {showFilters && (
        <FeedFilters
          teams={teams}
          postTypes={postTypes}
          tags={tags}
          selectedTeam={selectedTeam}
          selectedPostType={selectedPostType}
          selectedTag={selectedTag}
          onTeamChange={setSelectedTeam}
          onPostTypeChange={setSelectedPostType}
          onTagChange={setSelectedTag}
          onClearFilters={clearFilters}
          onApplyFilters={fetchPosts}
          onClose={() => setShowFilters(false)}
          colors={colors}
        />
      )}

      {/* Connection Status */}
      <ConnectionStatus onStatusChange={setConnectionStatus} />

      {error && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: colors.primary }]}
              onPress={fetchData}
              accessibilityRole="button"
              accessibilityLabel="Retry loading posts"
            >
              <Text style={[styles.retryButtonText, { color: colors.primaryForeground }]}>Retry</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.debugButton, { backgroundColor: colors.amber }]}
              onPress={async () => {
                try {
                  // Test API call using the Axios instance
                  console.log('Testing API call to /posts/feed');
                  const response = await api.get(endpoints.posts.feed, {
                    params: {
                      companyId: user?.companyId,
                    },
                  });
                  console.log('API response status:', response.status);
                  console.log('API response data:', response.data);
                  Alert.alert('API Test Result', JSON.stringify(response.data, null, 2));
                } catch (err: any) {
                  console.error('API call error:', err);
                  Alert.alert('API Test Error', err.message);
                }
              }}
            >
              <Text style={[styles.debugButtonText, { color: colors.foreground }]}>Test API</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>Loading posts...</Text>
        </View>
      ) : !error && (
        <FlatList
          data={posts}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <PostItem post={item} onTagPress={(tag) => setSelectedTag(tag)} />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      )}
    </SafeAreaView>
  );

  // Desktop layout with filters on the right
  const renderDesktopLayout = () => (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Connection Status */}
      <ConnectionStatus onStatusChange={setConnectionStatus} />

      {error && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: colors.primary }]}
              onPress={fetchData}
              accessibilityRole="button"
              accessibilityLabel="Retry loading posts"
            >
              <Text style={[styles.retryButtonText, { color: colors.primaryForeground }]}>Retry</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.debugButton, { backgroundColor: colors.amber }]}
              onPress={async () => {
                try {
                  // Test API call using the Axios instance
                  console.log('Testing API call to /posts/feed');
                  const response = await api.get(endpoints.posts.feed, {
                    params: {
                      companyId: user?.companyId,
                    },
                  });
                  console.log('API response status:', response.status);
                  console.log('API response data:', response.data);
                  Alert.alert('API Test Result', JSON.stringify(response.data, null, 2));
                } catch (err: any) {
                  console.error('API call error:', err);
                  Alert.alert('API Test Error', err.message);
                }
              }}
            >
              <Text style={[styles.debugButtonText, { color: colors.foreground }]}>Test API</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <View style={styles.desktopLayout}>
        {/* Main content area */}
        <View style={styles.desktopMainContent}>
          {loading && !refreshing ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>Loading posts...</Text>
            </View>
          ) : !error && (
            <FlatList
              data={posts}
              keyExtractor={(item) => item._id}
              renderItem={({ item }) => (
                <PostItem post={item} onTagPress={(tag) => setSelectedTag(tag)} />
              )}
              contentContainerStyle={styles.listContent}
              ListEmptyComponent={renderEmptyState}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={[colors.primary]}
                  tintColor={colors.primary}
                />
              }
            />
          )}
        </View>

        {/* Filters sidebar */}
        <View style={styles.desktopSidebar}>
          <FeedFilters
            teams={teams}
            postTypes={postTypes}
            tags={tags}
            selectedTeam={selectedTeam}
            selectedPostType={selectedPostType}
            selectedTag={selectedTag}
            onTeamChange={(value) => {
              setSelectedTeam(value);
              fetchPosts();
            }}
            onPostTypeChange={(value) => {
              setSelectedPostType(value);
              fetchPosts();
            }}
            onTagChange={(value) => {
              setSelectedTag(value);
              fetchPosts();
            }}
            onClearFilters={clearFilters}
            onApplyFilters={fetchPosts}
            onClose={() => {
              // In desktop mode, we might want to hide the sidebar or show a collapsed version
              // For now, we'll just leave it as a placeholder for future implementation
              console.log('Close desktop filters clicked');
            }}
            colors={colors}
            isDesktopLayout={true}
          />
        </View>
      </View>
    </SafeAreaView>
  );

  return isDesktopLayout ? renderDesktopLayout() : renderMobileLayout();
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  filterIcon: {
    marginRight: 6,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  filtersWrapper: {
    margin: 16,
    marginTop: 8,
    marginBottom: 16,
  },
  filtersContainer: {
    margin: 16,
    marginTop: 8,
    marginBottom: 16,
    padding: 16,
  },
  filtersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  filtersHeaderText: {
    fontSize: 18,
    fontWeight: '600',
  },
  filterSection: {
    marginBottom: 16,
  },
  filterTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  pickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  pickerIcon: {
    marginRight: 8,
  },
  filterList: {
    paddingBottom: 4,
    flexGrow: 1,
  },
  filterItem: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
  },
  filterItemText: {
    fontSize: 14,
    fontWeight: '500',
  },
  clearFiltersButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  clearFiltersText: {
    fontSize: 14,
    fontWeight: '500',
  },
  applyFiltersButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  applyFiltersText: {
    fontSize: 16,
    fontWeight: '600',
  },
  // Create Post styles
  createPostCard: {
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
    padding: 16,
  },
  createPostHeader: {
    marginBottom: 12,
  },
  createPostTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  createPostButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 12,
  },
  createPostIcon: {
    marginRight: 8,
  },
  createPostText: {
    fontSize: 16,
  },
  createPostFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  createPostAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  actionIcon: {
    marginRight: 6,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  emptyStateContainer: {
    padding: 16,
  },
  emptyStateCard: {
    padding: 24,
    alignItems: 'center',
    marginTop: 16,
  },
  emptyStateText: {
    textAlign: 'center',
    fontSize: 16,
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: '#FEE2E2', // Light red background for error
    borderRadius: 8,
    marginBottom: 24,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    color: '#B91C1C', // Red-700 equivalent
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  debugButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  debugButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },

  // Desktop layout styles
  desktopLayout: {
    flexDirection: 'row',
    flex: 1,
  },
  desktopMainContent: {
    flex: 3,
    paddingRight: 16,
  },
  desktopSidebar: {
    flex: 1,
    maxWidth: 300,
  },
});
