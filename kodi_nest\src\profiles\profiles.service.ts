import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Profile, ProfileDocument } from './schemas/profile.schema';
import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';

@Injectable()
export class ProfilesService {
  constructor(
    @InjectModel(Profile.name) private profileModel: Model<ProfileDocument>,
  ) {}

  async create(createProfileDto: CreateProfileDto): Promise<ProfileDocument> {
    const newProfile = new this.profileModel(createProfileDto);
    return newProfile.save();
  }

  async findAll(): Promise<ProfileDocument[]> {
    return this.profileModel.find().exec();
  }

  async findByCompany(companyId: string): Promise<ProfileDocument[]> {
    return this.profileModel.find({ companyId }).exec();
  }

  async findById(id: string): Promise<ProfileDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!id || id === 'undefined' || !/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid profile ID format: ${id}`);
    }

    const profile = await this.profileModel.findById(id).exec();
    if (!profile) {
      throw new NotFoundException(`Profile with ID ${id} not found`);
    }
    return profile;
  }

  async findByUserId(userId: string): Promise<ProfileDocument> {
    // Check if the userId is a valid ObjectId to prevent errors
    if (!userId || userId === 'undefined' || !/^[0-9a-fA-F]{24}$/.test(userId)) {
      throw new BadRequestException(`Invalid user ID format: ${userId}`);
    }

    const profile = await this.profileModel.findOne({ userId }).exec();
    if (!profile) {
      throw new NotFoundException(`Profile for user with ID ${userId} not found`);
    }
    return profile;
  }

  async update(id: string, updateProfileDto: UpdateProfileDto): Promise<ProfileDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!id || id === 'undefined' || !/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid profile ID format: ${id}`);
    }

    const updatedProfile = await this.profileModel
      .findByIdAndUpdate(id, updateProfileDto, { new: true })
      .exec();

    if (!updatedProfile) {
      throw new NotFoundException(`Profile with ID ${id} not found`);
    }

    return updatedProfile;
  }

  async updateUserSettings(userId: string, userSettings: any): Promise<ProfileDocument> {
    // Check if the userId is a valid ObjectId to prevent errors
    if (!userId || userId === 'undefined' || !/^[0-9a-fA-F]{24}$/.test(userId)) {
      throw new BadRequestException(`Invalid user ID format: ${userId}`);
    }

    const profile = await this.profileModel.findOne({ userId }).exec();

    if (!profile) {
      throw new NotFoundException(`Profile for user with ID ${userId} not found`);
    }

    profile.userSettings = { ...profile.userSettings, ...userSettings };
    return profile.save();
  }

  async remove(id: string): Promise<ProfileDocument> {
    const deletedProfile = await this.profileModel.findByIdAndDelete(id).exec();
    
    if (!deletedProfile) {
      throw new NotFoundException(`Profile with ID ${id} not found`);
    }
    
    return deletedProfile;
  }
}
