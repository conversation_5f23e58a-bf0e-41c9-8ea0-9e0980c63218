import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import api, { endpoints } from '@/src/api/api';

export default function TagsScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [tags, setTags] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchTags();
  }, []);

  const fetchTags = async () => {
    if (!user?.companyId) {
      setError('No company associated with your account');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data } = await api.get(endpoints.tags.byCompany(user.companyId));
      setTags(data);
    } catch (err: any) {
      console.error('Failed to fetch tags:', err);
      setError(err.response?.data?.message || 'Failed to load tags');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Tags</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {error ? (
          <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
            <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          </Card>
        ) : null}

        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>
              Loading tags...
            </Text>
          </Card>
        ) : (
          <Card style={styles.contentCard}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
              Company Tags
            </Text>
            <Text style={[styles.description, { color: colors.mutedForeground }]}>
              Tags are automatically created when used in posts. Here you can see all tags used in your company.
            </Text>
            
            {tags.length === 0 ? (
              <Text style={[styles.emptyText, { color: colors.mutedForeground }]}>
                No tags found. Tags will appear here when they are used in posts.
              </Text>
            ) : (
              <View style={styles.tagsContainer}>
                {tags.map((tag, index) => (
                  <View key={index} style={[styles.tagItem, { backgroundColor: colors.primary + '20' }]}>
                    <Text style={[styles.tagName, { color: colors.primary }]}>
                      #{tag.name}
                    </Text>
                    <Text style={[styles.tagCount, { color: colors.mutedForeground }]}>
                      {tag.count} {tag.count === 1 ? 'post' : 'posts'}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scrollContent: {
    padding: 20,
    gap: 16,
  },
  errorCard: {
    padding: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  loadingCard: {
    padding: 16,
    borderRadius: 8,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  contentCard: {
    padding: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  tagItem: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    alignItems: 'center',
  },
  tagName: {
    fontSize: 14,
    fontWeight: '600',
  },
  tagCount: {
    fontSize: 12,
    marginTop: 2,
  },
});
