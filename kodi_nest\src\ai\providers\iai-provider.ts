import { ChatMessage } from '../schemas/ai-chat.schema';

export interface IAiProvider {
  generateText(prompt: string, options?: { model?: string }): Promise<string>;
  createChatCompletion(messages: ChatMessage[], options?: { model?: string; temperature?: number }): Promise<ChatMessage | null>;
  transcribe(audioUrl: string, options?: { model?: string; language?: string }): Promise<string | null>;
}
