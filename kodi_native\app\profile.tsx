import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';

export default function ProfileScreen() {
  const { user, updateUserSettings } = useAuth();
  const { isDarkMode, theme, setTheme } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;

  const [firstName, setFirstName] = useState(user?.profile?.firstName || '');
  const [lastName, setLastName] = useState(user?.profile?.lastName || '');
  const [role, setRole] = useState(user?.profile?.role || '');
  const [avatar, setAvatar] = useState<string | null>(user?.profile?.avatar || null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showOnboarder, setShowOnboarder] = useState(user?.profile?.userSettings?.showOnboarder || false);

  const handleGoBack = () => {
    router.back();
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to change your avatar.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadAvatar(result.assets[0].uri);
    }
  };

  const uploadAvatar = async (uri: string) => {
    try {
      setLoading(true);

      const formData = new FormData();
      formData.append('avatar', {
        uri,
        type: 'image/jpeg',
        name: 'avatar.jpg',
      } as any);

      const response = await api.post(endpoints.uploads.avatar, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Control-Allow-Origin': '*',
        },
      });

      setAvatar(response.data.url);
    } catch (err) {
      console.error('Failed to upload avatar:', err);
      Alert.alert('Error', 'Failed to upload avatar. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const saveProfile = async () => {
    try {
      setLoading(true);
      setError('');

      const profileData = {
        firstName,
        lastName,
      };

      await api.patch(endpoints.profiles.byUserId(user!.id), profileData);

      Alert.alert('Success', 'Profile updated successfully');
    } catch (err: any) {
      console.error('Failed to update profile:', err);
      setError(err.response?.data?.message || 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <TouchableOpacity
              style={[styles.backButton, { backgroundColor: colors.secondary }]}
              onPress={handleGoBack}
            >
              <Ionicons name="arrow-back" size={24} color={colors.foreground} />
            </TouchableOpacity>
            <Text style={[styles.title, { color: colors.foreground }]}>Edit Profile</Text>
          </View>

          {error ? (
            <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
              <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
            </Card>
          ) : null}

          <Card style={styles.avatarCard}>
            <View style={styles.avatarContainer}>
              <View style={[styles.avatar, { backgroundColor: colors.muted }]}>
                {avatar ? (
                  <Image source={{ uri: avatar }} style={styles.avatarImage} />
                ) : (
                  <Text style={[styles.avatarPlaceholder, { color: colors.mutedForeground }]}>
                    {firstName?.[0] || lastName?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
                  </Text>
                )}
              </View>
              <TouchableOpacity
                style={[styles.changeAvatarButton, { backgroundColor: colors.primary }]}
                onPress={pickImage}
                disabled={loading}
              >
                <Ionicons name="camera" size={20} color={colors.primaryForeground} />
              </TouchableOpacity>
            </View>
            <Text style={[styles.avatarHint, { color: colors.mutedForeground }]}>
              Tap to change your profile picture
            </Text>
          </Card>

          <Card style={styles.formCard}>
            <Input
              label="First Name"
              value={firstName}
              onChangeText={setFirstName}
              placeholder="Enter your first name"
              leftIcon={<Ionicons name="person-outline" size={20} color={colors.mutedForeground} />}
            />

            <Input
              label="Last Name"
              value={lastName}
              onChangeText={setLastName}
              placeholder="Enter your last name"
              leftIcon={<Ionicons name="person-outline" size={20} color={colors.mutedForeground} />}
            />

            <Input
              label="Role"
              value={role}
              onChangeText={setRole}
              placeholder="Enter your role (e.g. Developer, Manager)"
              leftIcon={<Ionicons name="briefcase-outline" size={20} color={colors.mutedForeground} />}
            />

            <Input
              label="Email"
              value={user?.email}
              editable={false}
              leftIcon={<Ionicons name="mail-outline" size={20} color={colors.mutedForeground} />}
            />
          </Card>

          <Card style={styles.formCard}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>Settings</Text>

            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: colors.foreground }]}>
                  Onboarding Guide
                </Text>
                <Text style={[styles.settingDescription, { color: colors.mutedForeground }]}>
                  Show the onboarding guide to help you get started
                </Text>
              </View>
              <TouchableOpacity
                style={[
                  styles.toggle,
                  {
                    backgroundColor: showOnboarder ? colors.primary : colors.muted,
                  },
                ]}
                onPress={async () => {
                  const newValue = !showOnboarder;
                  setShowOnboarder(newValue);
                  try {
                    await updateUserSettings({
                      showOnboarder: newValue,
                    });
                  } catch (err) {
                    console.error('Failed to update onboarding settings:', err);
                    setShowOnboarder(!newValue); // Revert on error
                    Alert.alert('Error', 'Failed to update settings. Please try again.');
                  }
                }}
                activeOpacity={0.8}
              >
                <View
                  style={[
                    styles.toggleHandle,
                    {
                      backgroundColor: colors.card,
                      transform: [{ translateX: showOnboarder ? 16 : 0 }],
                    },
                  ]}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: colors.foreground }]}>
                  Dark Mode
                </Text>
                <Text style={[styles.settingDescription, { color: colors.mutedForeground }]}>
                  Use dark theme
                </Text>
              </View>
              <TouchableOpacity
                style={[
                  styles.toggle,
                  {
                    backgroundColor: isDarkMode ? colors.primary : colors.muted,
                  },
                ]}
                onPress={() => {
                  setTheme(isDarkMode ? 'light' : 'dark');
                }}
                activeOpacity={0.8}
                disabled={theme === 'system'}
              >
                <View
                  style={[
                    styles.toggleHandle,
                    {
                      backgroundColor: colors.card,
                      transform: [{ translateX: isDarkMode ? 16 : 0 }],
                    },
                  ]}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: colors.foreground }]}>
                  Use System Settings
                </Text>
                <Text style={[styles.settingDescription, { color: colors.mutedForeground }]}>
                  Follow system theme
                </Text>
              </View>
              <TouchableOpacity
                style={[
                  styles.toggle,
                  {
                    backgroundColor: theme === 'system' ? colors.primary : colors.muted,
                  },
                ]}
                onPress={() => {
                  setTheme(theme === 'system' ? (isDarkMode ? 'dark' : 'light') : 'system');
                }}
                activeOpacity={0.8}
              >
                <View
                  style={[
                    styles.toggleHandle,
                    {
                      backgroundColor: colors.card,
                      transform: [{ translateX: theme === 'system' ? 16 : 0 }],
                    },
                  ]}
                />
              </TouchableOpacity>
            </View>
          </Card>

          <Button
            title="Save Changes"
            onPress={saveProfile}
            isLoading={loading}
            style={styles.saveButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  errorCard: {
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
  },
  avatarCard: {
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  changeAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarHint: {
    fontSize: 14,
    marginTop: 8,
  },
  formCard: {
    marginBottom: 16,
  },
  saveButton: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  toggle: {
    width: 44,
    height: 24,
    borderRadius: 12,
    padding: 2,
  },
  toggleHandle: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
});
