# fly.toml app configuration file generated for kodickt on 2025-05-19T16:06:34-04:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'kodickt'
primary_region = 'iad'

[build]
  dockerfile = 'Dockerfile'

[env]
  NODE_ENV = 'production'
  PORT = '8080'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

  [http_service.concurrency]
    type = 'connections'
    hard_limit = 1000
    soft_limit = 500

[[vm]]
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
