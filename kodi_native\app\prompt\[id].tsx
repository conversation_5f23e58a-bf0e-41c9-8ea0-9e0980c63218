import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';
import { Prompt } from '@/src/types';

export default function PromptDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user, hasAdminRights } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editTemplate, setEditTemplate] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (id) {
      fetchPromptDetails();
    }
  }, [id]);

  const fetchPromptDetails = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await api.get(endpoints.prompts.byId(id));
      const promptData = response.data;
      setPrompt(promptData);
      
      // Set edit form values
      setEditName(promptData.name || '');
      setEditDescription(promptData.description || '');
      setEditTemplate(promptData.template || '');
      
    } catch (err: any) {
      console.error('Failed to fetch prompt details:', err);
      setError(err.response?.data?.message || 'Failed to load prompt details');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleEdit = () => {
    if (!hasAdminRights && prompt?.userId !== user?.id) {
      Alert.alert('Permission Denied', 'You can only edit your own prompts or need admin rights');
      return;
    }
    setEditModalVisible(true);
  };

  const handleSaveEdit = async () => {
    if (!editName.trim()) {
      Alert.alert('Error', 'Prompt name is required');
      return;
    }
    if (!editTemplate.trim()) {
      Alert.alert('Error', 'Prompt template is required');
      return;
    }

    try {
      setIsSaving(true);
      
      const updateData = {
        name: editName.trim(),
        description: editDescription.trim() || undefined,
        template: editTemplate.trim(),
      };
      
      const response = await api.patch(endpoints.prompts.byId(id), updateData);
      setPrompt(response.data);
      setEditModalVisible(false);
      Alert.alert('Success', 'Prompt updated successfully');
      
    } catch (err: any) {
      console.error('Failed to update prompt:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to update prompt');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = () => {
    if (!hasAdminRights && prompt?.userId !== user?.id) {
      Alert.alert('Permission Denied', 'You can only delete your own prompts or need admin rights');
      return;
    }

    Alert.alert(
      'Delete Prompt',
      `Are you sure you want to delete "${prompt?.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              await api.delete(endpoints.prompts.byId(id));
              Alert.alert('Success', 'Prompt deleted successfully');
              router.back();
            } catch (err: any) {
              console.error('Failed to delete prompt:', err);
              Alert.alert('Error', err.response?.data?.message || 'Failed to delete prompt');
            }
          }
        }
      ]
    );
  };

  const canEdit = hasAdminRights || prompt?.userId === user?.id;

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Prompt Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  if (error || !prompt) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Prompt Details</Text>
          <View style={styles.placeholder} />
        </View>
        <Card style={styles.errorCard}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>
            {error || 'Prompt not found'}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchPromptDetails}
            style={styles.retryButton}
          />
        </Card>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Prompt Details</Text>
        {canEdit && (
          <TouchableOpacity
            style={[styles.editButton, { backgroundColor: colors.primary }]}
            onPress={handleEdit}
          >
            <Ionicons name="create-outline" size={20} color={colors.primaryForeground} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Prompt Info Card */}
        <Card style={styles.promptCard}>
          <View style={styles.promptHeader}>
            <View style={[styles.promptIcon, { backgroundColor: colors.primary + '20' }]}>
              <Ionicons name="bulb" size={32} color={colors.primary} />
            </View>
            <View style={styles.promptInfo}>
              <Text style={[styles.promptName, { color: colors.foreground }]}>
                {prompt.name}
              </Text>
              {prompt.isSystem && (
                <View style={[styles.systemBadge, { backgroundColor: colors.accent + '20' }]}>
                  <Text style={[styles.systemText, { color: colors.accent }]}>
                    System Prompt
                  </Text>
                </View>
              )}
            </View>
          </View>
          
          {prompt.description && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
                Description
              </Text>
              <Text style={[styles.sectionContent, { color: colors.mutedForeground }]}>
                {prompt.description}
              </Text>
            </View>
          )}
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
              Template
            </Text>
            <View style={[styles.templateContainer, { backgroundColor: colors.secondary }]}>
              <Text style={[styles.templateText, { color: colors.foreground }]}>
                {prompt.template}
              </Text>
            </View>
          </View>

          {canEdit && (
            <View style={styles.adminActions}>
              <Button
                title="Edit Prompt"
                onPress={handleEdit}
                style={styles.editActionButton}
              />
              <Button
                title="Delete Prompt"
                onPress={handleDelete}
                variant="outline"
                style={[styles.deleteActionButton, { borderColor: colors.destructive }]}
                textStyle={{ color: colors.destructive }}
              />
            </View>
          )}
        </Card>
      </ScrollView>

      {/* Edit Modal */}
      <Modal
        visible={editModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setEditModalVisible(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setEditModalVisible(false)}>
              <Text style={[styles.modalCancelText, { color: colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.foreground }]}>Edit Prompt</Text>
            <TouchableOpacity onPress={handleSaveEdit} disabled={isSaving}>
              <Text style={[
                styles.modalSaveText, 
                { color: isSaving ? colors.mutedForeground : colors.primary }
              ]}>
                {isSaving ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <Input
              label="Name"
              value={editName}
              onChangeText={setEditName}
              placeholder="Enter prompt name"
              style={styles.modalInput}
            />
            
            <Input
              label="Description"
              value={editDescription}
              onChangeText={setEditDescription}
              placeholder="Enter description (optional)"
              multiline
              numberOfLines={3}
              style={styles.modalInput}
            />
            
            <Input
              label="Template"
              value={editTemplate}
              onChangeText={setEditTemplate}
              placeholder="Enter prompt template"
              multiline
              numberOfLines={6}
              style={styles.modalInput}
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorCard: {
    margin: 16,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  promptCard: {
    marginBottom: 16,
    padding: 20,
  },
  promptHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  promptIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  promptInfo: {
    flex: 1,
  },
  promptName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  systemBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  systemText: {
    fontSize: 12,
    fontWeight: '600',
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  templateContainer: {
    padding: 12,
    borderRadius: 8,
  },
  templateText: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: 'monospace',
  },
  adminActions: {
    marginTop: 20,
    gap: 12,
  },
  editActionButton: {
    marginBottom: 8,
  },
  deleteActionButton: {
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCancelText: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalInput: {
    marginBottom: 16,
  },
});
