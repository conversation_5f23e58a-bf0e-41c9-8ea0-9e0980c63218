import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';

export default function CreateTagScreen() {
  const { user, hasAdminRights } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [name, setName] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleGoBack = () => {
    router.back();
  };

  const handleSave = async () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to create tags');
      return;
    }

    if (!name.trim()) {
      Alert.alert('Error', 'Tag name is required');
      return;
    }

    // Remove # if user added it
    const cleanName = name.trim().replace(/^#/, '');

    try {
      setIsSaving(true);
      
      const newTag = {
        name: cleanName,
        companyId: user?.companyId,
      };
      
      await api.post(endpoints.tags.base, newTag);
      
      Alert.alert('Success', 'Tag created successfully', [
        { text: 'OK', onPress: () => router.back() }
      ]);
      
    } catch (err: any) {
      console.error('Failed to create tag:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to create tag');
    } finally {
      setIsSaving(false);
    }
  };

  const handleNameChange = (text: string) => {
    // Remove # if user types it, we'll add it in display
    const cleanText = text.replace(/^#/, '');
    setName(cleanText);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Create Tag</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <View style={styles.iconContainer}>
            <View style={[styles.tagIcon, { backgroundColor: colors.primary + '20' }]}>
              <Ionicons name="pricetag" size={48} color={colors.primary} />
            </View>
          </View>

          <Input
            label="Tag Name"
            value={name}
            onChangeText={handleNameChange}
            placeholder="Enter tag name (without #)"
            style={styles.input}
            leftIcon={<Text style={[styles.hashSymbol, { color: colors.primary }]}>#</Text>}
          />

          {name.trim() && (
            <View style={styles.previewSection}>
              <Text style={[styles.previewLabel, { color: colors.foreground }]}>
                Preview:
              </Text>
              <View style={[styles.tagPreview, { backgroundColor: colors.primary + '20' }]}>
                <Text style={[styles.tagPreviewText, { color: colors.primary }]}>
                  #{name.trim()}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.infoSection}>
            <View style={[styles.infoBox, { backgroundColor: colors.secondary }]}>
              <Ionicons name="information-circle" size={20} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.mutedForeground }]}>
                Tags help categorize and organize posts. They are automatically created when used in posts, 
                but you can pre-create them here for consistency.
              </Text>
            </View>
          </View>

          <View style={styles.actions}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleGoBack}
              style={styles.cancelButton}
            />
            <Button
              title="Create Tag"
              onPress={handleSave}
              isLoading={isSaving}
              style={styles.saveButton}
            />
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 24,
  },
  tagIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  input: {
    marginBottom: 16,
    width: '100%',
  },
  hashSymbol: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  previewSection: {
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  previewLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  tagPreview: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  tagPreviewText: {
    fontSize: 16,
    fontWeight: '600',
  },
  infoSection: {
    width: '100%',
    marginBottom: 24,
  },
  infoBox: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    alignItems: 'flex-start',
    gap: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
});
