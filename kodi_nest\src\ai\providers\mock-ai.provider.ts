import { IAiProvider } from './iai-provider';
import { ChatMessage } from '../schemas/ai-chat.schema';

export class MockAiProvider implements IAiProvider {
  async generateText(prompt: string, options?: { model?: string }): Promise<string> {
    return `Mocked AI response for prompt: "${prompt}" (model: ${options?.model || 'default_mock_model'})`;
  }

  async createChatCompletion(messages: ChatMessage[], options?: { model?: string; temperature?: number }): Promise<ChatMessage | null> {
    const lastMessage = messages[messages.length - 1]?.content || "no message";
    return {
      role: 'assistant',
      content: `Mocked AI chat response to: "${lastMessage}" (model: ${options?.model || 'default_mock_model'}, temperature: ${options?.temperature || 0.7})`,
      timestamp: new Date(),
    };
  }

  async transcribe(audioUrl: string, options?: { model?: string; language?: string }): Promise<string | null> {
    return `Mocked transcription for audio: ${audioUrl} (model: ${options?.model || 'default_mock_whisper'}, language: ${options?.language || 'en'})`;
  }
}
