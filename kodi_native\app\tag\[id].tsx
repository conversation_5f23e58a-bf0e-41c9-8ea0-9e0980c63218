import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';
import { Tag } from '@/src/types';

export default function TagDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user, hasAdminRights } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [tag, setTag] = useState<Tag | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editName, setEditName] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (id) {
      fetchTagDetails();
    }
  }, [id]);

  const fetchTagDetails = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await api.get(endpoints.tags.byId(id));
      const tagData = response.data;
      setTag(tagData);
      setEditName(tagData.name || '');
      
    } catch (err: any) {
      console.error('Failed to fetch tag details:', err);
      setError(err.response?.data?.message || 'Failed to load tag details');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleEdit = () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to edit tags');
      return;
    }
    setEditModalVisible(true);
  };

  const handleSaveEdit = async () => {
    if (!editName.trim()) {
      Alert.alert('Error', 'Tag name is required');
      return;
    }

    try {
      setIsSaving(true);
      
      const updateData = {
        name: editName.trim(),
      };
      
      const response = await api.patch(endpoints.tags.byId(id), updateData);
      setTag(response.data);
      setEditModalVisible(false);
      Alert.alert('Success', 'Tag updated successfully');
      
    } catch (err: any) {
      console.error('Failed to update tag:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to update tag');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to delete tags');
      return;
    }

    Alert.alert(
      'Delete Tag',
      `Are you sure you want to delete "#${tag?.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              await api.delete(endpoints.tags.byId(id));
              Alert.alert('Success', 'Tag deleted successfully');
              router.back();
            } catch (err: any) {
              console.error('Failed to delete tag:', err);
              Alert.alert('Error', err.response?.data?.message || 'Failed to delete tag');
            }
          }
        }
      ]
    );
  };

  const handleViewPosts = () => {
    // Navigate to posts filtered by this tag
    router.push(`/(tabs)?filter=tag&tagId=${id}`);
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Tag Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  if (error || !tag) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Tag Details</Text>
          <View style={styles.placeholder} />
        </View>
        <Card style={styles.errorCard}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>
            {error || 'Tag not found'}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchTagDetails}
            style={styles.retryButton}
          />
        </Card>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Tag Details</Text>
        {hasAdminRights && (
          <TouchableOpacity
            style={[styles.editButton, { backgroundColor: colors.primary }]}
            onPress={handleEdit}
          >
            <Ionicons name="create-outline" size={20} color={colors.primaryForeground} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Tag Info Card */}
        <Card style={styles.tagCard}>
          <View style={styles.tagHeader}>
            <View style={[styles.tagIcon, { backgroundColor: colors.primary + '20' }]}>
              <Ionicons name="pricetag" size={32} color={colors.primary} />
            </View>
            <View style={styles.tagInfo}>
              <Text style={[styles.tagName, { color: colors.foreground }]}>
                #{tag.name}
              </Text>
              <Text style={[styles.tagCount, { color: colors.mutedForeground }]}>
                {tag.count} posts
              </Text>
            </View>
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            <Button
              title="View Posts"
              onPress={handleViewPosts}
              style={styles.actionButton}
            />
          </View>

          {hasAdminRights && (
            <View style={styles.adminActions}>
              <Button
                title="Edit Tag"
                onPress={handleEdit}
                style={styles.editActionButton}
              />
              <Button
                title="Delete Tag"
                onPress={handleDelete}
                variant="outline"
                style={[styles.deleteActionButton, { borderColor: colors.destructive }]}
                textStyle={{ color: colors.destructive }}
              />
            </View>
          )}
        </Card>
      </ScrollView>

      {/* Edit Modal */}
      <Modal
        visible={editModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setEditModalVisible(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setEditModalVisible(false)}>
              <Text style={[styles.modalCancelText, { color: colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.foreground }]}>Edit Tag</Text>
            <TouchableOpacity onPress={handleSaveEdit} disabled={isSaving}>
              <Text style={[
                styles.modalSaveText, 
                { color: isSaving ? colors.mutedForeground : colors.primary }
              ]}>
                {isSaving ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <Input
              label="Tag Name"
              value={editName}
              onChangeText={setEditName}
              placeholder="Enter tag name"
              style={styles.modalInput}
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorCard: {
    margin: 16,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  tagCard: {
    marginBottom: 16,
    padding: 20,
  },
  tagHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  tagIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  tagInfo: {
    flex: 1,
  },
  tagName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  tagCount: {
    fontSize: 16,
  },
  actions: {
    marginBottom: 20,
  },
  actionButton: {
    marginBottom: 8,
  },
  adminActions: {
    marginTop: 20,
    gap: 12,
  },
  editActionButton: {
    marginBottom: 8,
  },
  deleteActionButton: {
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCancelText: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalInput: {
    marginBottom: 16,
  },
});
