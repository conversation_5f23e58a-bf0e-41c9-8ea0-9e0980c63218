import React from "react";
import DiscussFilterDisplay from "./DiscussFilterDisplay";
import DiscussConversation, { Message } from "./DiscussConversation";
import DiscussInput from "./DiscussInput";
import { useTracker } from "meteor/react-meteor-data";
import dbCollections from "../../../imports/db/dbCollections";
import makeFilterQuery from "../Filters/filterQuery.js";
import { Messaging } from "../Messaging/messaging";

interface Props {

}

export default function Discuss(props: Props) {
    const [awaitingAi, setAwaitingAi] = React.useState(false);
    const [pendingMessage, setPendingMessage] = React.useState<string>("");

    useTracker(() => { return Meteor.subscribe("aiChats") });
    const chat = useTracker(() => {
        const query = {
            userId: Meteor.userId(),
            tag: Session.get('TagFilterName') || { $in: [null, undefined] },
            teamId: Session.get('TeamFilterId') || { $in: [null, undefined] },
            postTypeId: Session.get('TypeFilterId') || { $in: [null, undefined] },
            timeRange: Session.get('SelectedTimeRange') || { $in: [null, undefined] },
            archived: false
        };
        return dbCollections.AIChats.findOne(query);
    })
    
    useTracker(() => {
        makeFilterQuery();
    })

    const messages: Message[] = chat?.thread ?? [];

    function onSend(message: string) {
        setAwaitingAi(true);
        setPendingMessage(message);
        const args = {
            tag: Session.get('TagFilterName'),
            teamId: Session.get('TeamFilterId'),
            postTypeId: Session.get('TypeFilterId'),
            timeRange: Session.get('SelectedTimeRange'),
            message: message
        }
        Meteor.call("sendAiChat", args, (err, res) => {
            if (err) {
                Messaging.error(err);
            }
            setAwaitingAi(false);
            setPendingMessage("");
        })
    }

    return (
        <div className="relative flex flex-col justify-between mx-4 min-h-0 h-full">
            <DiscussFilterDisplay />
            <DiscussConversation awaitingAi={awaitingAi} messages={messages} pendingMessage={pendingMessage} />
            <DiscussInput ready={!awaitingAi} onSend={onSend} />
        </div>
    )
}