/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/ai-chat`; params?: Router.UnknownInputParams; } | { pathname: `/company`; params?: Router.UnknownInputParams; } | { pathname: `/create-post-type`; params?: Router.UnknownInputParams; } | { pathname: `/create-prompt`; params?: Router.UnknownInputParams; } | { pathname: `/create-tag`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/people`; params?: Router.UnknownInputParams; } | { pathname: `/post-type-selection`; params?: Router.UnknownInputParams; } | { pathname: `/post-types`; params?: Router.UnknownInputParams; } | { pathname: `/pre-record`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/prompts`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/tags`; params?: Router.UnknownInputParams; } | { pathname: `/teams`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/analysis` | `/analysis`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/chat` | `/chat`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/drafts` | `/drafts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/post` | `/post`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/post-type/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/prompt/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/tag/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/team/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/ai-chat`; params?: Router.UnknownOutputParams; } | { pathname: `/company`; params?: Router.UnknownOutputParams; } | { pathname: `/create-post-type`; params?: Router.UnknownOutputParams; } | { pathname: `/create-prompt`; params?: Router.UnknownOutputParams; } | { pathname: `/create-tag`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/people`; params?: Router.UnknownOutputParams; } | { pathname: `/post-type-selection`; params?: Router.UnknownOutputParams; } | { pathname: `/post-types`; params?: Router.UnknownOutputParams; } | { pathname: `/pre-record`; params?: Router.UnknownOutputParams; } | { pathname: `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/prompts`; params?: Router.UnknownOutputParams; } | { pathname: `/register`; params?: Router.UnknownOutputParams; } | { pathname: `/tags`; params?: Router.UnknownOutputParams; } | { pathname: `/teams`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/analysis` | `/analysis`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/chat` | `/chat`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/drafts` | `/drafts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/post` | `/post`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/post-type/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/prompt/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/tag/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/team/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/ai-chat${`?${string}` | `#${string}` | ''}` | `/company${`?${string}` | `#${string}` | ''}` | `/create-post-type${`?${string}` | `#${string}` | ''}` | `/create-prompt${`?${string}` | `#${string}` | ''}` | `/create-tag${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/people${`?${string}` | `#${string}` | ''}` | `/post-type-selection${`?${string}` | `#${string}` | ''}` | `/post-types${`?${string}` | `#${string}` | ''}` | `/pre-record${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/prompts${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `/tags${`?${string}` | `#${string}` | ''}` | `/teams${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/analysis${`?${string}` | `#${string}` | ''}` | `/analysis${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/chat${`?${string}` | `#${string}` | ''}` | `/chat${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/drafts${`?${string}` | `#${string}` | ''}` | `/drafts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/post${`?${string}` | `#${string}` | ''}` | `/post${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/ai-chat`; params?: Router.UnknownInputParams; } | { pathname: `/company`; params?: Router.UnknownInputParams; } | { pathname: `/create-post-type`; params?: Router.UnknownInputParams; } | { pathname: `/create-prompt`; params?: Router.UnknownInputParams; } | { pathname: `/create-tag`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/people`; params?: Router.UnknownInputParams; } | { pathname: `/post-type-selection`; params?: Router.UnknownInputParams; } | { pathname: `/post-types`; params?: Router.UnknownInputParams; } | { pathname: `/pre-record`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/prompts`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/tags`; params?: Router.UnknownInputParams; } | { pathname: `/teams`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/analysis` | `/analysis`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/chat` | `/chat`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/drafts` | `/drafts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/post` | `/post`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | `/+not-found` | `/post-type/${Router.SingleRoutePart<T>}` | `/prompt/${Router.SingleRoutePart<T>}` | `/tag/${Router.SingleRoutePart<T>}` | `/team/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/post-type/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/prompt/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/tag/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/team/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
