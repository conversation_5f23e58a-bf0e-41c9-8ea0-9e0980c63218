import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import api, { endpoints } from '@/src/api/api';

export default function PeopleScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [people, setPeople] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchPeople();
  }, []);

  const fetchPeople = async () => {
    if (!user?.companyId) {
      setError('No company associated with your account');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data } = await api.get(`/users?companyId=${user.companyId}`);
      setPeople(data);
    } catch (err: any) {
      console.error('Failed to fetch people:', err);
      setError(err.response?.data?.message || 'Failed to load people');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>People</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {error ? (
          <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
            <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          </Card>
        ) : null}

        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>
              Loading people...
            </Text>
          </Card>
        ) : (
          <Card style={styles.contentCard}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
              Company Members
            </Text>
            {people.length === 0 ? (
              <Text style={[styles.emptyText, { color: colors.mutedForeground }]}>
                No people found in your company.
              </Text>
            ) : (
              people.map((person, index) => (
                <View key={index} style={[styles.personItem, { borderBottomColor: colors.border }]}>
                  <View style={styles.personInfo}>
                    <Text style={[styles.personName, { color: colors.foreground }]}>
                      {person.profile?.firstName} {person.profile?.lastName}
                    </Text>
                    <Text style={[styles.personEmail, { color: colors.mutedForeground }]}>
                      {person.email}
                    </Text>
                  </View>
                  <View style={styles.personRoles}>
                    {person.roles?.map((role: string, roleIndex: number) => (
                      <View key={roleIndex} style={[styles.roleTag, { backgroundColor: colors.primary + '20' }]}>
                        <Text style={[styles.roleText, { color: colors.primary }]}>{role}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              ))
            )}
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scrollContent: {
    padding: 20,
    gap: 16,
  },
  errorCard: {
    padding: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  loadingCard: {
    padding: 16,
    borderRadius: 8,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  contentCard: {
    padding: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  personItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  personInfo: {
    flex: 1,
  },
  personName: {
    fontSize: 16,
    fontWeight: '600',
  },
  personEmail: {
    fontSize: 14,
    marginTop: 2,
  },
  personRoles: {
    flexDirection: 'row',
    gap: 4,
  },
  roleTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
