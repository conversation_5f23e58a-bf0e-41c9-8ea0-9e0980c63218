import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';
import { PostType } from '@/src/types';

export default function PostTypeDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user, hasAdminRights } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [postType, setPostType] = useState<PostType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editRecordTips, setEditRecordTips] = useState('');
  const [editHighlightColor, setEditHighlightColor] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (id) {
      fetchPostTypeDetails();
    }
  }, [id]);

  const fetchPostTypeDetails = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await api.get(endpoints.postTypes.byId(id));
      const postTypeData = response.data;
      setPostType(postTypeData);
      
      // Set edit form values
      setEditName(postTypeData.name || '');
      setEditDescription(postTypeData.description || '');
      setEditRecordTips(postTypeData.recordTips || '');
      setEditHighlightColor(postTypeData.highlightColor || '');
      
    } catch (err: any) {
      console.error('Failed to fetch post type details:', err);
      setError(err.response?.data?.message || 'Failed to load post type details');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleEdit = () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to edit post types');
      return;
    }
    setEditModalVisible(true);
  };

  const handleSaveEdit = async () => {
    if (!editName.trim()) {
      Alert.alert('Error', 'Post type name is required');
      return;
    }

    try {
      setIsSaving(true);
      
      const updateData = {
        name: editName.trim(),
        description: editDescription.trim() || undefined,
        recordTips: editRecordTips.trim() || undefined,
        highlightColor: editHighlightColor.trim() || undefined,
      };
      
      const response = await api.patch(endpoints.postTypes.byId(id), updateData);
      setPostType(response.data);
      setEditModalVisible(false);
      Alert.alert('Success', 'Post type updated successfully');
      
    } catch (err: any) {
      console.error('Failed to update post type:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to update post type');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to delete post types');
      return;
    }

    Alert.alert(
      'Delete Post Type',
      `Are you sure you want to delete "${postType?.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              await api.delete(endpoints.postTypes.byId(id));
              Alert.alert('Success', 'Post type deleted successfully');
              router.back();
            } catch (err: any) {
              console.error('Failed to delete post type:', err);
              Alert.alert('Error', err.response?.data?.message || 'Failed to delete post type');
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Post Type Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  if (error || !postType) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Post Type Details</Text>
          <View style={styles.placeholder} />
        </View>
        <Card style={styles.errorCard}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>
            {error || 'Post type not found'}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchPostTypeDetails}
            style={styles.retryButton}
          />
        </Card>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Post Type Details</Text>
        {hasAdminRights && (
          <TouchableOpacity
            style={[styles.editButton, { backgroundColor: colors.primary }]}
            onPress={handleEdit}
          >
            <Ionicons name="create-outline" size={20} color={colors.primaryForeground} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Post Type Info Card */}
        <Card style={styles.postTypeCard}>
          <View style={styles.postTypeHeader}>
            <View style={[
              styles.postTypeIcon, 
              { 
                backgroundColor: postType.highlightColor 
                  ? postType.highlightColor + '20' 
                  : colors.primary + '20' 
              }
            ]}>
              <Text style={[
                styles.postTypeIconText, 
                { color: postType.highlightColor || colors.primary }
              ]}>
                {postType.name[0].toUpperCase()}
              </Text>
            </View>
            <View style={styles.postTypeInfo}>
              <Text style={[styles.postTypeName, { color: colors.foreground }]}>
                {postType.name}
              </Text>
              {postType.highlightColor && (
                <View style={styles.colorIndicator}>
                  <View 
                    style={[
                      styles.colorSwatch, 
                      { backgroundColor: postType.highlightColor }
                    ]} 
                  />
                  <Text style={[styles.colorText, { color: colors.mutedForeground }]}>
                    {postType.highlightColor}
                  </Text>
                </View>
              )}
            </View>
          </View>
          
          {postType.description && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
                Description
              </Text>
              <Text style={[styles.sectionContent, { color: colors.mutedForeground }]}>
                {postType.description}
              </Text>
            </View>
          )}
          
          {postType.recordTips && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
                Recording Tips
              </Text>
              <Text style={[styles.sectionContent, { color: colors.mutedForeground }]}>
                {postType.recordTips}
              </Text>
            </View>
          )}

          {hasAdminRights && (
            <View style={styles.adminActions}>
              <Button
                title="Edit Post Type"
                onPress={handleEdit}
                style={styles.editActionButton}
              />
              <Button
                title="Delete Post Type"
                onPress={handleDelete}
                variant="outline"
                style={[styles.deleteActionButton, { borderColor: colors.destructive }]}
                textStyle={{ color: colors.destructive }}
              />
            </View>
          )}
        </Card>
      </ScrollView>

      {/* Edit Modal */}
      <Modal
        visible={editModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setEditModalVisible(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setEditModalVisible(false)}>
              <Text style={[styles.modalCancelText, { color: colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.foreground }]}>Edit Post Type</Text>
            <TouchableOpacity onPress={handleSaveEdit} disabled={isSaving}>
              <Text style={[
                styles.modalSaveText, 
                { color: isSaving ? colors.mutedForeground : colors.primary }
              ]}>
                {isSaving ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <Input
              label="Name"
              value={editName}
              onChangeText={setEditName}
              placeholder="Enter post type name"
              style={styles.modalInput}
            />
            
            <Input
              label="Description"
              value={editDescription}
              onChangeText={setEditDescription}
              placeholder="Enter description (optional)"
              multiline
              numberOfLines={3}
              style={styles.modalInput}
            />
            
            <Input
              label="Recording Tips"
              value={editRecordTips}
              onChangeText={setEditRecordTips}
              placeholder="Enter recording tips (optional)"
              multiline
              numberOfLines={3}
              style={styles.modalInput}
            />
            
            <Input
              label="Highlight Color"
              value={editHighlightColor}
              onChangeText={setEditHighlightColor}
              placeholder="Enter color (e.g., #FF5733)"
              style={styles.modalInput}
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorCard: {
    margin: 16,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  postTypeCard: {
    marginBottom: 16,
    padding: 20,
  },
  postTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  postTypeIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  postTypeIconText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  postTypeInfo: {
    flex: 1,
  },
  postTypeName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  colorIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorSwatch: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  colorText: {
    fontSize: 14,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  adminActions: {
    marginTop: 20,
    gap: 12,
  },
  editActionButton: {
    marginBottom: 8,
  },
  deleteActionButton: {
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCancelText: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalInput: {
    marginBottom: 16,
  },
});
