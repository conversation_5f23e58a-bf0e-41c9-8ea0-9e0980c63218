{"compilerOptions": {"target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "noEmit": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "allowSyntheticDefaultImports": true, "strict": true, "jsx": "react-native", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]}