import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { MongooseExceptionFilter } from './common/filters/mongoose-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Serve static files from the uploads directory
  app.useStaticAssets(join(process.cwd(), 'uploads'), {
    prefix: '/uploads',
  });

  // Define development origins for CORS
  const developmentOrigins = [
    // Next.js frontend
    'http://kodi.seakaytee.com',
    'http://localhost:3002',
    // Expo web development server ports
    'http://localhost:19000',
    'http://localhost:19006',
    'http://localhost:8081',
    //'https://kodiql.fly.dev',
    //'https://kodickt.fly.dev',
    'https://kodi.seakaytee.com',
    // Include all possible Expo web ports
    /^http:\/\/localhost:(19\d{3}|8\d{3})$/,
    // Allow any local IP for development
    /^http:\/\/192\.168\.\d+\.\d+:(19\d{3}|8\d{3}|3010|3002)$/,
    // Allow any IP with standard ports during development
    /^http:\/\/\d+\.\d+\.\d+\.\d+:(19\d{3}|8\d{3}|3010|3002)$/,
  ];

    let allowedOrigins: (string | RegExp)[] = [];

    // Always include production frontend domains
    allowedOrigins.push('https://kodickt.fly.dev'); // <-- YOUR FRONTEND DOMAIN HERE
    allowedOrigins.push('https://kodi.seakaytee.com'); // Add if this is also a frontend domain
    allowedOrigins.push('http://localhost');

    // Enable CORS with more permissive settings
    app.enableCors({
      origin: (origin, callback) => {
        // Allow requests with no origin (e.g., from mobile apps or same-origin if applicable)
        if (!origin) {
          callback(null, true);
          return;
        }
        // Check if the requesting origin is in our allowed list (string or regex match)
        const isAllowed = allowedOrigins.some((allowed) => {
          if (typeof allowed === 'string') {
            return allowed === origin;
          }
          return allowed.test(origin);
        });

        if (isAllowed) {
          callback(null, true);
        } else {
          // Log the blocked origin for debugging
          console.error(`CORS: Origin ${origin} not allowed`);
          callback(new Error('Not allowed by CORS'));
        }
      },
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    exposedHeaders: ['Content-Disposition'],
  });

  // Set global prefix but exclude certain routes
  const apiPrefix = process.env.API_PREFIX || 'api';
  app.setGlobalPrefix(apiPrefix, {
    exclude: ['/health'], // Exclude the health endpoint from the global prefix
  });

  // Enable validation
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    transform: true,
    forbidNonWhitelisted: true,
  }));

  // Add global exception filters
  app.useGlobalFilters(new MongooseExceptionFilter());

  // Start the server
  const port = parseInt(process.env.PORT || '8080', 10);
  const host = process.env.HOST || '0.0.0.0';

  console.log(`Attempting to start server on ${host}:${port}`);
  await app.listen(8080,'0.0.0.0');

  // Get the actual address the server is listening on
  const serverUrl = await app.getUrl();
  console.log(`Application is running on: ${serverUrl}/${apiPrefix}`);
}
bootstrap();
