# Meteor packages used by this project, one per line.
# Check this file (and the other files in this directory) into your repository.
#
# 'meteor add' and 'meteor remove' will edit this file for you,
# but you can also edit it by hand.

meteor-base@1.5.2             # Packages every Meteor app needs to have
mobile-experience@1.1.2       # Packages for a great mobile UX
mongo@2.0.2                  # The database Meteor supports right now
blaze-html-templates          # Compile .html files into Meteor Blaze views
jquery                        # Wrapper package for npm-installed jquery
reactive-var@1.0.13           # Reactive variable for tracker
reactive-dict@1.3.2
tracker@1.3.4                 # Meteor's client-side reactive programming library

standard-minifier-css@1.9.3   # CSS minifier run for production mode
standard-minifier-js@3.0.0    # JS minifier run for production mode
ecmascript@0.16.9             # Enable ECMAScript2015+ syntax in app code
typescript@5.4.3              # Enable TypeScript syntax in .ts and .tsx modules
shell-server@0.6.0            # Server-side component of the `meteor shell` command


hot-module-replacement@0.5.4  # Update code in development without reloading the page
blaze-hot                     # Update files using Blaze's API with HMR

session@1.2.2
aldeed:collection2
aldeed:autoform@7.1.0!
aldeed:schema-deny@4.0.2
mdg:validated-method
accounts-password@3.0.2
socialize:base-model
socialize:checkable
socialize:linkable-model
socialize:friendships
socialize:messaging
socialize:user-model
socialize:requestable
socialize:user-blocking
socialize:postable
socialize:likeable
socialize:commentable
socialize:user-presence
socialize:user-profile
socialize:feed
alanning:roles
accounts-ui@1.4.3
ostrio:flow-router-extra@3.10.1
useraccounts:tailwind
seakaytee:flow-routing-extra
seakaytee:autoform-tw-color
seakaytee:socialize-follow
edgee:slingshot
kadira:blaze-layout
#seakaytee:autoform-slingshot
seakaytee:autoform-trix
react-template-helper
react-meteor-data
#3.0 hwillson:stub-collections
#dburles:factory@1.5.0
seakaytee:restivus
email@3.1.0
aldeed:simple-schema@2.0.0
