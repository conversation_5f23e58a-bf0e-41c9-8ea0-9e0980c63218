import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { useAudioRecorder, useAudioPlayer } from 'expo-audio';
import { Ionicons } from '@expo/vector-icons';
import api, { endpoints } from '@/src/api/api';

interface AudioInputFieldProps {
  onAudioProcessed: (text: string) => void;
  placeholder?: string;
  label?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  style?: any;
  inputStyle?: any;
  labelStyle?: any;
  colors: any;
}

export default function AudioInputField({
  onAudioProcessed,
  placeholder = 'Enter text or record audio...',
  label,
  value,
  onChangeText,
  style,
  inputStyle,
  labelStyle,
  colors,
}: AudioInputFieldProps) {
  const [audioUri, setAudioUri] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [recordingTime, setRecordingTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Recorder hook
  const recorder = useAudioRecorder({
    extension: '.m4a',
    sampleRate: 44100,
    numberOfChannels: 1,
    bitRate: 128000,
    android: {
      outputFormat: 'mpeg4',
      audioEncoder: 'aac',
    },
    ios: {
      outputFormat: 'aac ',
      audioQuality: 96,
    },
  });
  // Player hook (source is set dynamically)
  const player = useAudioPlayer();

  // Timer for recording duration
  useEffect(() => {
    if (recorder.isRecording) {
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000) as unknown as NodeJS.Timeout;
    } else if (timerRef.current) {
      clearInterval(timerRef.current as unknown as number);
      timerRef.current = null;
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current as unknown as number);
        timerRef.current = null;
      }
    };
  }, [recorder.isRecording]);

  useEffect(() => {
    // When recording stops, set the audio URI
    if (!recorder.isRecording && recorder.uri) {
      setAudioUri(recorder.uri);
    }
  }, [recorder.isRecording, recorder.uri]);

  const handleStartRecording = async () => {
    setError('');
    setAudioUri(null);
    try {
      recorder.record();
    } catch (err: any) {
      setError(err.message || 'Failed to start recording.');
    }
  };

  const handleStopRecording = async () => {
    try {
      await recorder.stop();
    } catch (err: any) {
      setError(err.message || 'Failed to stop recording.');
    }
  };

  const handleCancelRecording = async () => {
    if (recorder.isRecording) await recorder.stop();
    setAudioUri(null);
  };

  const handlePlayAudio = async () => {
    if (audioUri) {
      player.replace(audioUri);
      player.play();
    }
  };

  const handlePauseAudio = async () => {
    player.pause();
  };

  const handleStopAudio = async () => {
    player.pause();
    player.replace(null);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const processAudio = async () => {
    if (!audioUri) return;
    setIsProcessing(true);
    setError('');
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: audioUri,
        type: 'audio/m4a',
        name: 'recording.m4a',
      } as any);
      const uploadResponse = await api.post(endpoints.uploads.audioUpload, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Control-Allow-Origin': '*',
        },
      });
      const audioUrl = uploadResponse.data.url;
      const transcribeResponse = await api.post(endpoints.ai.transcribe, {
        audioUrl,
      });
      const transcription = transcribeResponse.data.text;
      onAudioProcessed(transcription);
      setAudioUri(null);
    } catch (err: any) {
      console.error('Error processing audio:', err);
      setError(err.response?.data?.message || 'Failed to process audio. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <View style={[styles.container, style]}>
      {label && (
        <Text style={[styles.label, { color: colors.foreground }, labelStyle]}>
          {label}
        </Text>
      )}
      <View style={styles.inputContainer}>
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={colors.mutedForeground}
          style={[
            styles.input,
            {
              color: colors.foreground,
              borderColor: colors.border,
              backgroundColor: colors.card,
            },
            inputStyle,
          ]}
          editable={!recorder.isRecording && !isProcessing}
        />
        {!recorder.isRecording && !audioUri && !isProcessing && (
          <TouchableOpacity
            style={styles.recordButton}
            onPress={handleStartRecording}
            accessibilityRole="button"
            accessibilityLabel="Record audio"
          >
            <Ionicons name="mic-outline" size={20} color={colors.mutedForeground} />
          </TouchableOpacity>
        )}
      </View>
      {error ? (
        <Text style={[styles.errorText, { color: colors.destructive }]}>
          {error}
        </Text>
      ) : null}
      {recorder.isRecording && (
        <View style={styles.recordingContainer}>
          <View style={[styles.recordingIndicator, { backgroundColor: colors.destructive }]} />
          <Text style={[styles.recordingText, { color: colors.destructive }]}>Recording... {formatTime(recordingTime)}</Text>
          <TouchableOpacity
            style={[styles.stopButton, { backgroundColor: colors.muted }]}
            onPress={handleStopRecording}
          >
            <Text style={[styles.buttonText, { color: colors.foreground }]}>Stop</Text>
          </TouchableOpacity>
        </View>
      )}
      {audioUri && !isProcessing && (
        <View style={styles.audioControlsContainer}>
          <View style={styles.audioPlayerPlaceholder}>
            <Text style={[styles.audioReadyText, { color: colors.foreground }]}>Audio recording ready</Text>
            {!player.playing ? (
              <TouchableOpacity onPress={handlePlayAudio}>
                <Ionicons name="play" size={24} color={colors.primary} />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity onPress={handlePauseAudio}>
                <Ionicons name="pause" size={24} color={colors.primary} />
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={handleStopAudio}>
              <Ionicons name="stop" size={24} color={colors.destructive} />
            </TouchableOpacity>
          </View>
          <View style={styles.audioButtonsContainer}>
            <TouchableOpacity
              style={[styles.useButton, { backgroundColor: colors.primary }]}
              onPress={processAudio}
            >
              <Text style={[styles.buttonText, { color: colors.primaryForeground }]}>Use Recording</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: colors.muted }]}
              onPress={handleCancelRecording}
            >
              <Text style={[styles.buttonText, { color: colors.foreground }]}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
      {isProcessing && (
        <View style={styles.processingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.processingText, { color: colors.mutedForeground }]}>Processing audio...</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    flex: 1,
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingRight: 40,
    fontSize: 16,
  },
  recordButton: {
    position: 'absolute',
    right: 12,
    padding: 4,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  recordingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  recordingIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  recordingText: {
    fontSize: 14,
    flex: 1,
  },
  stopButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  buttonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  audioControlsContainer: {
    marginTop: 8,
  },
  audioPlayerPlaceholder: {
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
    marginBottom: 8,
  },
  audioReadyText: {
    fontSize: 14,
  },
  audioButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  useButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 4,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 4,
    alignItems: 'center',
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  processingText: {
    fontSize: 14,
    marginLeft: 8,
  },
});
