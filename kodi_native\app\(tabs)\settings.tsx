import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';

interface SettingsSection {
  id: string;
  title: string;
  icon: string;
  description: string;
  screen?: string;
  action?: () => void;
}

export default function SettingsScreen() {
  const { user, logout } = useAuth();
  const { isDarkMode, theme, setTheme } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;

  const [loading, setLoading] = useState(false);
  const [companyName, setCompanyName] = useState<string | null>(null);

  useEffect(() => {
    if (user?.companyId) {
      fetchCompanyInfo();
    }
  }, [user]);

  const fetchCompanyInfo = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(endpoints.companies.byId(user!.companyId!));
      setCompanyName(data.name);
    } catch (err) {
      console.error('Failed to fetch company info:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: logout,
          style: 'destructive',
        },
      ]
    );
  };

  const toggleDarkMode = () => {
    setTheme(isDarkMode ? 'light' : 'dark');
  };

  const toggleSystemTheme = () => {
    setTheme(theme === 'system' ? (isDarkMode ? 'dark' : 'light') : 'system');
  };

  const settingsSections: SettingsSection[] = [
    {
      id: 'account',
      title: 'Account Settings',
      icon: 'person',
      description: 'Manage your personal account settings',
      screen: '/profile',
    },
    {
      id: 'people',
      title: 'People',
      icon: 'people',
      description: 'Manage users and permissions',
      screen: '/people',
    },
    {
      id: 'teams',
      title: 'Teams',
      icon: 'people-circle',
      description: 'Create and manage teams',
      screen: '/teams',
    },
    {
      id: 'tags',
      title: 'Tags',
      icon: 'pricetag',
      description: 'Manage post tags and categories',
      screen: '/tags',
    },
    {
      id: 'post-types',
      title: 'Post Types',
      icon: 'document-text',
      description: 'Configure post types and templates',
      screen: '/post-types',
    },
    {
      id: 'prompts',
      title: 'Prompts',
      icon: 'chatbubble-ellipses',
      description: 'Manage AI prompts and templates',
      screen: '/prompts',
    },
    {
      id: 'company',
      title: 'Company',
      icon: 'business',
      description: 'Manage company information and settings',
      screen: '/company',
    },
    {
      id: 'appearance',
      title: 'Appearance',
      icon: 'color-palette',
      description: 'Customize the look and feel of the application',
      screen: '/profile', // Redirects to profile since we moved appearance settings there
    },
    {
      id: 'logout',
      title: 'Logout',
      icon: 'log-out',
      description: 'Sign out of your account',
      action: handleLogout,
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.foreground }]}>Settings</Text>
          <TouchableOpacity
            style={[styles.userIconButton, { backgroundColor: colors.secondary }]}
            onPress={() => router.push('/profile')}
          >
            {user?.profile?.avatar ? (
              <Image
                source={{ uri: user.profile.avatar }}
                style={styles.userIcon}
              />
            ) : (
              <Text style={[styles.userIconText, { color: colors.foreground }]}>
                {user?.profile?.firstName?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.gridContainer}>
          {settingsSections.map((section) => (
            <TouchableOpacity
              key={section.id}
              style={[styles.gridItem, { backgroundColor: colors.card }]}
              onPress={() => {
                if (section.screen) {
                  console.log('Navigating to:', section.screen);
                  router.push(section.screen as any);
                } else {
                  section.action?.();
                }
              }}
            >
              <View style={styles.gridItemContent}>
                <View style={[styles.gridItemIcon, { backgroundColor: colors.primary + '10' }]}>
                  <Ionicons name={section.icon as any} size={24} color={colors.primary} />
                </View>
                <Text style={[styles.gridItemTitle, { color: colors.foreground }]}>
                  {section.title}
                </Text>
                <Text style={[styles.gridItemDescription, { color: colors.mutedForeground }]}>
                  {section.description}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.mutedForeground }]}>
            Kodi App v1.0.0
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  userIconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  userIconText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
  },
  gridItem: {
    width: '50%',
    padding: 8,
  },
  gridItemContent: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    justifyContent: 'center',
  },
  gridItemIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  gridItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  gridItemDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: 16,
    alignItems: 'center',
    marginTop: 24,
  },
  footerText: {
    fontSize: 14,
  },
});
