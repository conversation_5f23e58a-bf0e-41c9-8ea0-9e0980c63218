import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import api, { endpoints } from '@/src/api/api';

export default function PromptsScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [prompts, setPrompts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchPrompts();
  }, []);

  const fetchPrompts = async () => {
    if (!user?.companyId) {
      setError('No company associated with your account');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      // Note: This endpoint may not exist yet, but we'll create a placeholder
      const { data } = await api.get(`/prompts?companyId=${user.companyId}`);
      setPrompts(data);
    } catch (err: any) {
      console.error('Failed to fetch prompts:', err);
      // For now, we'll show a placeholder message since the endpoint might not exist
      setPrompts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  // Placeholder prompts for demonstration
  const placeholderPrompts = [
    {
      id: 1,
      name: 'Daily Standup',
      description: 'Template for daily standup meetings',
      template: 'What did you work on yesterday? What are you working on today? Any blockers?',
      category: 'Meeting'
    },
    {
      id: 2,
      name: 'Project Update',
      description: 'Template for project status updates',
      template: 'Project status, key achievements, upcoming milestones, risks and issues',
      category: 'Project'
    },
    {
      id: 3,
      name: 'Feedback Request',
      description: 'Template for requesting feedback',
      template: 'What feedback are you looking for? What context should reviewers know?',
      category: 'Feedback'
    }
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Prompts</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {error ? (
          <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
            <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          </Card>
        ) : null}

        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>
              Loading prompts...
            </Text>
          </Card>
        ) : (
          <Card style={styles.contentCard}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
              AI Prompts & Templates
            </Text>
            <Text style={[styles.description, { color: colors.mutedForeground }]}>
              Manage AI prompts and templates to help guide post creation and analysis.
            </Text>
            
            <View style={styles.promptsContainer}>
              {placeholderPrompts.map((prompt, index) => (
                <View key={index} style={[styles.promptItem, { backgroundColor: colors.secondary }]}>
                  <View style={styles.promptHeader}>
                    <Text style={[styles.promptName, { color: colors.foreground }]}>
                      {prompt.name}
                    </Text>
                    <View style={[styles.categoryBadge, { backgroundColor: colors.primary + '20' }]}>
                      <Text style={[styles.categoryText, { color: colors.primary }]}>
                        {prompt.category}
                      </Text>
                    </View>
                  </View>
                  
                  <Text style={[styles.promptDescription, { color: colors.mutedForeground }]}>
                    {prompt.description}
                  </Text>
                  
                  <View style={[styles.templateContainer, { backgroundColor: colors.background }]}>
                    <Text style={[styles.templateLabel, { color: colors.primary }]}>
                      Template:
                    </Text>
                    <Text style={[styles.templateText, { color: colors.mutedForeground }]}>
                      {prompt.template}
                    </Text>
                  </View>
                  
                  <View style={styles.promptActions}>
                    <TouchableOpacity 
                      style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
                      onPress={() => Alert.alert('Edit Prompt', 'Edit functionality coming soon')}
                    >
                      <Ionicons name="pencil" size={16} color={colors.primary} />
                      <Text style={[styles.actionText, { color: colors.primary }]}>Edit</Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity 
                      style={[styles.actionButton, { backgroundColor: colors.accent + '20' }]}
                      onPress={() => Alert.alert('Use Prompt', 'Use prompt functionality coming soon')}
                    >
                      <Ionicons name="play" size={16} color={colors.accent} />
                      <Text style={[styles.actionText, { color: colors.accent }]}>Use</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
            
            <TouchableOpacity 
              style={[styles.addButton, { backgroundColor: colors.primary }]}
              onPress={() => Alert.alert('Add Prompt', 'Add new prompt functionality coming soon')}
            >
              <Ionicons name="add" size={20} color={colors.primaryForeground} />
              <Text style={[styles.addButtonText, { color: colors.primaryForeground }]}>
                Add New Prompt
              </Text>
            </TouchableOpacity>
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scrollContent: {
    padding: 20,
    gap: 16,
  },
  errorCard: {
    padding: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  loadingCard: {
    padding: 16,
    borderRadius: 8,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  contentCard: {
    padding: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  promptsContainer: {
    gap: 16,
    marginBottom: 20,
  },
  promptItem: {
    padding: 16,
    borderRadius: 8,
  },
  promptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  promptName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  promptDescription: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  templateContainer: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 12,
  },
  templateLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  templateText: {
    fontSize: 12,
    lineHeight: 16,
    fontStyle: 'italic',
  },
  promptActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
