import { ExceptionFilter, Catch, ArgumentsHost, HttpStatus, Logger } from '@nestjs/common';
import { Response } from 'express';
import { Error as MongooseError } from 'mongoose';

@Catch(MongooseError.CastError, MongooseError.ValidationError)
export class MongooseExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(MongooseExceptionFilter.name);

  catch(exception: MongooseError.CastError | MongooseError.ValidationError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.BAD_REQUEST;
    let message = 'Invalid data provided';
    let details: any = {};

    if (exception instanceof MongooseError.CastError) {
      // Handle ObjectId casting errors
      if (exception.kind === 'ObjectId') {
        message = `Invalid ${exception.path} format. Expected a valid ObjectId.`;
        details = {
          field: exception.path,
          value: exception.value,
          expectedType: 'ObjectId',
        };
      } else {
        message = `Invalid ${exception.path} format.`;
        details = {
          field: exception.path,
          value: exception.value,
          expectedType: exception.kind,
        };
      }
    } else if (exception instanceof MongooseError.ValidationError) {
      // Handle validation errors
      message = 'Validation failed';
      details = Object.keys(exception.errors).reduce((acc, key) => {
        const error = exception.errors[key];
        acc[key] = error.message;
        return acc;
      }, {} as Record<string, string>);
    }

    // Log the error for debugging
    this.logger.error(`Mongoose error: ${message}`, {
      url: request.url,
      method: request.method,
      details,
      stack: exception.stack,
    });

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message,
      details,
    });
  }
}
